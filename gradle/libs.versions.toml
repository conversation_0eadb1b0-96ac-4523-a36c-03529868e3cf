[versions]
agp = "8.10.1"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.1"
material = "1.12.0"
googleAndroidLibrariesMapsplatformSecretsGradlePlugin = "2.0.1"
playServicesMaps = "18.1.0"
constraintlayout = "2.2.1"
swiperefreshlayout = "1.1.0"
supportAnnotations = "28.0.0"
<<<<<<< HEAD
secretsGradlePlugin = "2.0.1"
playServicesMaps = "18.2.0"
constraintlayout = "2.1.4"
=======
volley = "1.2.1"
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
play-services-maps = { group = "com.google.android.gms", name = "play-services-maps", version.ref = "playServicesMaps" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
swiperefreshlayout = { group = "androidx.swiperefreshlayout", name = "swiperefreshlayout", version.ref = "swiperefreshlayout" }
support-annotations = { group = "com.android.support", name = "support-annotations", version.ref = "supportAnnotations" }
<<<<<<< HEAD
play-services-maps = { group = "com.google.android.gms", name = "play-services-maps", version.ref = "playServicesMaps" }
constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
google-android-libraries-mapsplatform-secrets-gradle-plugin = { id = "com.google.android.libraries.mapsplatform.secrets-gradle-plugin", version.ref = "secretsGradlePlugin" }

=======
volley = { group = "com.android.volley", name = "volley", version.ref = "volley" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
google-android-libraries-mapsplatform-secrets-gradle-plugin = { id = "com.google.android.libraries.mapsplatform.secrets-gradle-plugin", version.ref = "googleAndroidLibrariesMapsplatformSecretsGradlePlugin" }
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
