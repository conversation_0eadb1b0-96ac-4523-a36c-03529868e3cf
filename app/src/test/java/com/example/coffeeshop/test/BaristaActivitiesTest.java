package com.example.coffeeshop.test;

import android.content.Intent;
import android.content.Context;
import com.example.coffeeshop.activities.*;

/**
 * Simple test to verify all barista activities can be instantiated and launched
 */
public class BaristaActivitiesTest {
    
    public static void testActivitiesInstantiation(Context context) {
        try {
            // Test all barista activities
            Intent dashboardIntent = new Intent(context, BaristaDashboardActivity.class);
            Intent queueIntent = new Intent(context, BaristaOrderQueueActivity.class);
            Intent inventoryIntent = new Intent(context, BaristaInventoryActivity.class);
            Intent readyOrdersIntent = new Intent(context, BaristaReadyOrdersActivity.class);
            Intent dailyTasksIntent = new Intent(context, BaristaDailyTasksActivity.class);
            Intent quickActionsIntent = new Intent(context, BaristaQuickActionsActivity.class);
            
            System.out.println("All barista activities can be instantiated successfully!");
            
        } catch (Exception e) {
            System.err.println("Error instantiating barista activities: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
