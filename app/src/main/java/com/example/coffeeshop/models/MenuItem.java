package com.example.coffeeshop.models;

import android.os.Parcel;
import android.os.Parcelable;
import androidx.annotation.NonNull;
import org.json.JSONObject;
import java.util.Locale;

public class MenuItem implements Parcelable {
    private int menuItemId;
    private String name;
    private String description;
    private double price;
    private String category;
    private String imageUrl;
    private boolean available;
    private String status;
    private String createdAt;
    private String updatedAt;

    // Default constructor
    public MenuItem() {}

    // Full constructor
    public MenuItem(int menuItemId, String name, String description, double price, 
                   String category, String imageUrl, boolean available, String status,
                   String createdAt, String updatedAt) {
        this.menuItemId = menuItemId;
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.imageUrl = imageUrl;
        this.available = available;
        this.status = status;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    // JSON constructor
    public MenuItem(JSONObject json) throws Exception {
        // Try both possible field names for ID (menu_item_id from our API, id as fallback)
        if (json.has("menu_item_id")) {
            this.menuItemId = json.getInt("menu_item_id");
        } else {
            this.menuItemId = json.getInt("id");
        }
        
        this.name = json.getString("name");
        this.description = json.optString("description", "");
        this.price = json.getDouble("price");
        this.category = json.getString("category");
        this.imageUrl = json.optString("image_url", "");
        
        // Handle available field - it might be boolean or integer (1/0)
        if (json.get("available") instanceof Boolean) {
            this.available = json.getBoolean("available");
        } else {
            this.available = json.getInt("available") == 1;
        }
        
        this.status = json.optString("status", "active"); // Default to active if not provided
        this.createdAt = json.optString("created_at", "");
        this.updatedAt = json.optString("updated_at", "");
    }

    // Parcelable implementation
    protected MenuItem(Parcel in) {
        menuItemId = in.readInt();
        name = in.readString();
        description = in.readString();
        price = in.readDouble();
        category = in.readString();
        imageUrl = in.readString();
        available = in.readByte() != 0;
        status = in.readString();
        createdAt = in.readString();
        updatedAt = in.readString();
    }

    public static final Creator<MenuItem> CREATOR = new Creator<>() {
        @Override
        public MenuItem createFromParcel(Parcel in) {
            return new MenuItem(in);
        }

        @Override
        public MenuItem[] newArray(int size) {
            return new MenuItem[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(menuItemId);
        dest.writeString(name);
        dest.writeString(description);
        dest.writeDouble(price);
        dest.writeString(category);
        dest.writeString(imageUrl);
        dest.writeByte((byte) (available ? 1 : 0));
        dest.writeString(status);
        dest.writeString(createdAt);
        dest.writeString(updatedAt);
    }

    // Getters and Setters
    public int getMenuItemId() {
        return menuItemId;
    }

    public void setMenuItemId(int menuItemId) {
        this.menuItemId = menuItemId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean isAvailable() {
        return available;
    }

    public void setAvailable(boolean available) {
        this.available = available;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Add getId() method that maps to getMenuItemId()
    public int getId() {
        return menuItemId;
    }

    // Utility methods
    public String getFormattedPrice() {
        return String.format(Locale.getDefault(), "$%.2f", price);
    }

    public String getStatusDisplayName() {
        switch (status.toLowerCase()) {
            case "active":
                return "Active";
            case "inactive":
                return "Inactive";
            case "out_of_stock":
                return "Out of Stock";
            default:
                return status;
        }
    }

    public String getAvailabilityDisplayName() {
        return available ? "Available" : "Unavailable";
    }

    public boolean hasImage() {
        return imageUrl != null && !imageUrl.trim().isEmpty() && !imageUrl.equals("null");
    }

    public boolean isActive() {
        return "active".equalsIgnoreCase(status) && available;
    }

    public boolean isOutOfStock() {
        return "out_of_stock".equalsIgnoreCase(status);
    }

    public boolean isInactive() {
        return "inactive".equalsIgnoreCase(status);
    }

    public String getShortDescription() {
        if (description == null || description.trim().isEmpty()) {
            return "No description available";
        }
        if (description.length() > 50) {
            return description.substring(0, 47) + "...";
        }
        return description;
    }

    // Simple constructor for mock data (add this to MenuItem.java)
    public MenuItem(int menuItemId, String name, String description, double price,
                    String category, String imageUrl, boolean available, String status) {
        this.menuItemId = menuItemId;
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.imageUrl = imageUrl;
        this.available = available;
        this.status = status;
        this.createdAt = "2024-01-01"; // Default value
        this.updatedAt = "2024-01-01"; // Default value
    }

    public int getStatusColor() {
        switch (status.toLowerCase()) {
            case "active":
                return android.R.color.holo_green_light;
            case "inactive":
                return android.R.color.darker_gray;
            case "out_of_stock":
                return android.R.color.holo_red_light;
            default:
                return android.R.color.darker_gray;
        }
    }

    @NonNull
    @Override
    public String toString() {
        return "MenuItem{" +
                "menuItemId=" + menuItemId +
                ", name='" + name + '\'' +
                ", price=" + price +
                ", category='" + category + '\'' +
                ", available=" + available +
                ", status='" + status + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MenuItem menuItem = (MenuItem) o;
        return menuItemId == menuItem.menuItemId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(menuItemId);
    }
}
