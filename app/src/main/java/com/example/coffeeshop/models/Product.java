package com.example.coffeeshop.models;

import java.math.BigDecimal;
import java.util.Date;

public class Product {
    private int itemId;
    private String name;
    private String description;
    private BigDecimal price;
    private String category;
    private boolean available;
    private int quantityAvailable;
    private Date lastUpdated;
    private int createdBy;
    private String imageUrl;

    // Constructor
    public Product(int itemId, String name, String description, BigDecimal price, String category, 
                   boolean available, int quantityAvailable, Date lastUpdated, int createdBy, String imageUrl) {
        this.itemId = itemId;
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.available = available;
        this.quantityAvailable = quantityAvailable;
        this.lastUpdated = lastUpdated;
        this.createdBy = createdBy;
        this.imageUrl = imageUrl;
    }

    // Constructor without itemId (for new products)
    public Product(String name, String description, BigDecimal price, String category, 
                   boolean available, int quantityAvailable, Date lastUpdated, int createdBy, String imageUrl) {
        this.name = name;
        this.description = description;
        this.price = price;
        this.category = category;
        this.available = available;
        this.quantityAvailable = quantityAvailable;
        this.lastUpdated = lastUpdated;
        this.createdBy = createdBy;
        this.imageUrl = imageUrl;
    }

    // Getters and Setters
    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public boolean isAvailable() {
        return available;
    }

    public void setAvailable(boolean available) {
        this.available = available;
    }

    public int getQuantityAvailable() {
        return quantityAvailable;
    }

    public void setQuantityAvailable(int quantityAvailable) {
        this.quantityAvailable = quantityAvailable;
    }

    public Date getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Date lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }
    
    // Alias method for compatibility
    public int getProductId() {
        return itemId;
    }
}
