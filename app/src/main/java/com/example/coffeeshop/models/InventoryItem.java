package com.example.coffeeshop.models;

public class InventoryItem {
    private int itemId;
    private String name;
    private int currentStock;
    private int minStockLevel;
    private String unit; // "kg", "liters", "pieces", etc.
    private boolean isLowStock;

    public InventoryItem(int itemId, String name, int currentStock, int minStockLevel, String unit) {
        this.itemId = itemId;
        this.name = name;
        this.currentStock = currentStock;
        this.minStockLevel = minStockLevel;
        this.unit = unit;
        this.isLowStock = currentStock <= minStockLevel;
    }

    // Getters and setters
    public int getItemId() { return itemId; }
    public void setItemId(int itemId) { this.itemId = itemId; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public int getCurrentStock() { return currentStock; }
    public void setCurrentStock(int currentStock) { 
        this.currentStock = currentStock;
        this.isLowStock = currentStock <= minStockLevel;
    }

    public int getQuantity() { return currentStock; } // Alias for getCurrentStock
    public String getCategory() { return "Supplies"; } // Default category
    public int getMinStock() { return minStockLevel; } // Alias for getMinStockLevel

    public int getMinStockLevel() { return minStockLevel; }
    public void setMinStockLevel(int minStockLevel) { this.minStockLevel = minStockLevel; }

    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }

    public boolean isLowStock() { return isLowStock; }
    public void setLowStock(boolean lowStock) { isLowStock = lowStock; }

    public boolean isOutOfStock() { return currentStock <= 0; }
}
