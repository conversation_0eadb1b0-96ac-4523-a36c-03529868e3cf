package com.example.coffeeshop.models;

public class Role {
    public static final int CUSTOMER = 1;
    public static final int BARISTA = 2;
    public static final int SHIPPER = 3;
    public static final int MANAGER = 4;
    public static final int CUSTOMER_SUPPORT = 5;

    private int roleId;
    private String roleName;

    public Role(int roleId, String roleName) {
        this.roleId = roleId;
        this.roleName = roleName;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public static String getRoleName(int roleId) {
        switch (roleId) {
            case CUSTOMER:
                return "Customer";
            case BARISTA:
                return "Barista";
            case SHIPPER:
                return "Shipper";
            case MANAGER:
                return "Manager";
            case CUSTOMER_SUPPORT:
                return "Customer Support";
            default:
                return "Unknown";
        }
    }
}
