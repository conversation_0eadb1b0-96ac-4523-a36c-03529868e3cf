package com.example.coffeeshop.models;

public class RecipeStep {
    private int stepNumber;
    private int recipeId;
    private String instruction;
    private String tip;
    private int durationSeconds; // Duration for this step

    // Constructors
    public RecipeStep() {}

    public RecipeStep(int stepNumber, int recipeId, String instruction, int durationSeconds) {
        this.stepNumber = stepNumber;
        this.recipeId = recipeId;
        this.instruction = instruction;
        this.durationSeconds = durationSeconds;
    }

    public RecipeStep(int stepNumber, String instruction) {
        this.stepNumber = stepNumber;
        this.instruction = instruction;
    }

    public RecipeStep(int stepNumber, String instruction, String tip, int durationSeconds) {
        this.stepNumber = stepNumber;
        this.instruction = instruction;
        this.tip = tip;
        this.durationSeconds = durationSeconds;
    }

    // Getters and Setters
    public int getStepNumber() {
        return stepNumber;
    }

    public void setStepNumber(int stepNumber) {
        this.stepNumber = stepNumber;
    }

    public int getRecipeId() {
        return recipeId;
    }

    public void setRecipeId(int recipeId) {
        this.recipeId = recipeId;
    }

    public String getInstruction() {
        return instruction;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    public String getTip() {
        return tip;
    }

    public void setTip(String tip) {
        this.tip = tip;
    }

    public int getDurationSeconds() {
        return durationSeconds;
    }

    public void setDurationSeconds(int durationSeconds) {
        this.durationSeconds = durationSeconds;
    }

    // Utility methods
    public String getFormattedDuration() {
        if (durationSeconds <= 0) return "";
        
        int minutes = durationSeconds / 60;
        int seconds = durationSeconds % 60;
        
        if (minutes > 0) {
            return String.format("%d:%02d", minutes, seconds);
        } else {
            return String.format("%ds", seconds);
        }
    }

    public boolean hasTip() {
        return tip != null && !tip.trim().isEmpty();
    }

    public boolean hasTimer() {
        return durationSeconds > 0;
    }
}
