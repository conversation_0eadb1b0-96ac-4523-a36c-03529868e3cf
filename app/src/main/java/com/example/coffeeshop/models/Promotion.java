package com.example.coffeeshop.models;

import org.json.JSONObject;

public class Promotion {
    private int promotionId;
    private String title;
    private String description;
    private String discountType; // "percentage", "fixed_amount", "free_shipping"
    private double discountValue;
    private double minimumOrderAmount;
    private String startDate;
    private String endDate;
    private boolean isActive;
    private Integer usageLimit;
    private int currentUsage;
    private String applicableProducts;
    private String promoCode;
    private String createdAt;
    private String updatedAt;

    public Promotion() {
        // Empty constructor
    }

    public Promotion(JSONObject json) {
        try {
            this.promotionId = json.optInt("promotion_id", 0);
            this.title = json.optString("title", "");
            this.description = json.optString("description", "");
            this.discountType = json.optString("discount_type", "");
            this.discountValue = json.optDouble("discount_value", 0.0);
            this.minimumOrderAmount = json.optDouble("minimum_order_amount", 0.0);
            this.startDate = json.optString("start_date", "");
            this.endDate = json.optString("end_date", "");
            this.isActive = json.optInt("is_active", 0) == 1;
            
            // Handle null values for usage_limit
            if (!json.isNull("usage_limit")) {
                this.usageLimit = json.optInt("usage_limit", 0);
            }
            
            this.currentUsage = json.optInt("current_usage", 0);
            this.applicableProducts = json.optString("applicable_products", null);
            this.promoCode = json.optString("promo_code", null);
            this.createdAt = json.optString("created_at", "");
            this.updatedAt = json.optString("updated_at", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // Getters
    public int getPromotionId() { return promotionId; }
    public String getTitle() { return title; }
    public String getDescription() { return description; }
    public String getDiscountType() { return discountType; }
    public double getDiscountValue() { return discountValue; }
    public double getMinimumOrderAmount() { return minimumOrderAmount; }
    public String getStartDate() { return startDate; }
    public String getEndDate() { return endDate; }
    public boolean isActive() { return isActive; }
    public Integer getUsageLimit() { return usageLimit; }
    public int getCurrentUsage() { return currentUsage; }
    public String getApplicableProducts() { return applicableProducts; }
    public String getPromoCode() { return promoCode; }
    public String getCreatedAt() { return createdAt; }
    public String getUpdatedAt() { return updatedAt; }

    // Setters
    public void setPromotionId(int promotionId) { this.promotionId = promotionId; }
    public void setTitle(String title) { this.title = title; }
    public void setDescription(String description) { this.description = description; }
    public void setDiscountType(String discountType) { this.discountType = discountType; }
    public void setDiscountValue(double discountValue) { this.discountValue = discountValue; }
    public void setMinimumOrderAmount(double minimumOrderAmount) { this.minimumOrderAmount = minimumOrderAmount; }
    public void setStartDate(String startDate) { this.startDate = startDate; }
    public void setEndDate(String endDate) { this.endDate = endDate; }
    public void setActive(boolean active) { isActive = active; }
    public void setUsageLimit(Integer usageLimit) { this.usageLimit = usageLimit; }
    public void setCurrentUsage(int currentUsage) { this.currentUsage = currentUsage; }
    public void setApplicableProducts(String applicableProducts) { this.applicableProducts = applicableProducts; }
    public void setPromoCode(String promoCode) { this.promoCode = promoCode; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }

    // Helper methods
    public String getDiscountDisplayText() {
        if ("percentage".equals(discountType)) {
            return (int) discountValue + "% OFF";
        } else if ("fixed_amount".equals(discountType)) {
            return "$" + String.format("%.2f", discountValue) + " OFF";
        } else if ("free_shipping".equals(discountType)) {
            return "FREE SHIPPING";
        }
        return "DISCOUNT";
    }

    public String getUsageDisplayText() {
        if (usageLimit == null) {
            return "Unlimited usage";
        }
        return currentUsage + "/" + usageLimit + " used";
    }

    public boolean hasPromoCode() {
        return promoCode != null && !promoCode.trim().isEmpty();
    }

    public boolean isExpired() {
        // This is a simple check - in a real app you'd compare with current date
        return !isActive;
    }

    public double calculateDiscount(double orderAmount) {
        if (!isActive || orderAmount < minimumOrderAmount) {
            return 0.0;
        }

        if ("percentage".equals(discountType)) {
            return orderAmount * (discountValue / 100.0);
        } else if ("fixed_amount".equals(discountType)) {
            return Math.min(discountValue, orderAmount);
        }
        
        return 0.0; // free_shipping doesn't affect order amount in this context
    }
}
