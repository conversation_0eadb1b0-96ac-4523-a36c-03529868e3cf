<<<<<<< HEAD
package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperViewAssignedOrdersActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_view_assigned_orders);

        // Bắt sự kiện nút Details của đơn hàng ORD001
        Button btnDetails1 = findViewById(R.id.btn_details);
        btnDetails1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ShipperViewAssignedOrdersActivity.this, ShipperOrderDetailsActivity.class);
                // Tạm thời truyền dữ liệu ảo nếu cần
                intent.putExtra("orderId", "ORD001");
                intent.putExtra("customer", "<PERSON>");
                intent.putExtra("address", "123 Main Street");
                intent.putExtra("status", "Accepted");
                startActivity(intent);
            }
        });

        // Bắt sự kiện nút Details của đơn hàng ORD002
        Button btnDetails2 = findViewById(R.id.btn_details_2);
        btnDetails2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ShipperViewAssignedOrdersActivity.this, ShipperOrderDetailsActivity.class);
                intent.putExtra("orderId", "ORD002");
                intent.putExtra("customer", "Alice Smith");
                intent.putExtra("address", "456 Coffee Lane");
                intent.putExtra("status", "Rejected");
                startActivity(intent);
            }
        });
    }
}
=======
package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.AssignedOrderAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.models.Order;

import org.json.JSONArray;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ShipperViewAssignedOrdersActivity extends BaseActivity {

    private RecyclerView rvOrders;
    private SwipeRefreshLayout swipeRefresh;
    private TextView tvEmpty;
    private ProgressBar progressBar;

    private final List<Order> assignedOrders = new ArrayList<>();
    private AssignedOrderAdapter adapter;

    private ApiService apiService;

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_view_assigned_orders);

        initViews();
        setupRecyclerView();
        setupSwipeRefresh();
        setupBottomNav();
        setSelectedNavItem(R.id.nav_assigned_orders);

        apiService = new ApiService();

        fetchAssignedOrders();
    }

    @Override
    protected void onResume() {
        super.onResume();
        setSelectedNavItem(R.id.nav_assigned_orders);
    }

    private void initViews() {
        rvOrders = findViewById(R.id.recycler_view);
        swipeRefresh = findViewById(R.id.swipe_refresh);
        tvEmpty = findViewById(R.id.tv_empty);
        progressBar = findViewById(R.id.progress_bar);
    }

    private void setupRecyclerView() {
        adapter = new AssignedOrderAdapter(this, assignedOrders, order -> markOrderAsCompleted(order));
        rvOrders.setLayoutManager(new LinearLayoutManager(this));
        rvOrders.setAdapter(adapter);
    }

    private void setupSwipeRefresh() {
        swipeRefresh.setOnRefreshListener(this::fetchAssignedOrders);
    }

    private void fetchAssignedOrders() {
        progressBar.setVisibility(View.VISIBLE);
        tvEmpty.setVisibility(View.GONE);
        rvOrders.setVisibility(View.GONE);

        apiService.getAssignedOrders(new ApiService.OrdersCallback() {
            @Override
            public void onSuccess(JSONArray ordersJson) {
                parseOrders(ordersJson);
            }

            @Override
            public void onError(String error) {
                progressBar.setVisibility(View.GONE);
                swipeRefresh.setRefreshing(false);
                tvEmpty.setVisibility(View.VISIBLE);
                tvEmpty.setText(error);
            }
        });
    }

    private void parseOrders(JSONArray response) {
        try {
            assignedOrders.clear();

            for (int i = 0; i < response.length(); i++) {
                JSONObject obj = response.getJSONObject(i);
                Order order = new Order();
                order.setOrderId(obj.getInt("order_id"));
                order.setCustomerName(obj.optString("customer_name", ""));
                order.setStatus(obj.optString("status", ""));
                order.setTotalPrice(BigDecimal.valueOf(obj.optDouble("total_price", 0.0)));
                String createdAtStr = obj.optString("created_at", "");
                String phoneNumber = obj.optString("customer_phone","");
                order.setCustomerPhone(phoneNumber);
                if (!createdAtStr.isEmpty()) {
                    try {
                        Date createdAtDate = sdf.parse(createdAtStr);
                        order.setCreatedAt(createdAtDate);
                    } catch (Exception e) {
                        order.setCreatedAt(null);
                    }
                }
                assignedOrders.add(order);
            }

            progressBar.setVisibility(View.GONE);
            swipeRefresh.setRefreshing(false);

            if (assignedOrders.isEmpty()) {
                tvEmpty.setVisibility(View.VISIBLE);
                rvOrders.setVisibility(View.GONE);
            } else {
                tvEmpty.setVisibility(View.GONE);
                rvOrders.setVisibility(View.VISIBLE);
            }

            adapter.notifyDataSetChanged();

        } catch (Exception e) {
            e.printStackTrace();
            progressBar.setVisibility(View.GONE);
            swipeRefresh.setRefreshing(false);
            tvEmpty.setVisibility(View.VISIBLE);
            tvEmpty.setText("Error parsing orders");
        }
    }

    private void markOrderAsCompleted(Order order) {
        progressBar.setVisibility(View.VISIBLE);
        apiService.markOrderAsCompleted(order.getOrderId(), new ApiService.SimpleCallback() {
            @Override
            public void onSuccess() {
                progressBar.setVisibility(View.GONE);
                assignedOrders.remove(order);
                adapter.notifyDataSetChanged();
                Toast.makeText(ShipperViewAssignedOrdersActivity.this,
                        "Order marked as completed", Toast.LENGTH_SHORT).show();

                if (assignedOrders.isEmpty()) {
                    tvEmpty.setVisibility(View.VISIBLE);
                    rvOrders.setVisibility(View.GONE);
                }
            }

            @Override
            public void onError(String error) {
                progressBar.setVisibility(View.GONE);
                Toast.makeText(ShipperViewAssignedOrdersActivity.this,
                        "Failed: " + error, Toast.LENGTH_SHORT).show();
            }
        });
    }
}
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
