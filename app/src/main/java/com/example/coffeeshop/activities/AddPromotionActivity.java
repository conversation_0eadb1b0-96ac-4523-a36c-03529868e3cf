package com.example.coffeeshop.activities;

import android.app.DatePickerDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class AddPromotionActivity extends AppCompatActivity {

    private static final String TAG = "AddPromotionActivity";

    private Toolbar toolbar;
    private TextInputEditText etPromotionCode, etPromotionName, etPromotionValue;
    private TextInputEditText etBuyQuantity, etGetQuantity, etStartDate, etEndDate, etUsageLimit;
    private AutoCompleteTextView spinnerPromotionType, spinnerApplicableProducts, spinnerStatus;
    private TextInputLayout layoutPromotionValue;
    private LinearLayout layoutBuyXGetY; // Changed from TextInputLayout to LinearLayout
    private MaterialButton btnCancel, btnSave;

    private Calendar startCalendar = Calendar.getInstance();
    private Calendar endCalendar = Calendar.getInstance();
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    
    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "Starting onCreate");
            setContentView(R.layout.activity_add_promotion);
            Log.d(TAG, "Layout inflated successfully");

            initViews();
            Log.d(TAG, "Views initialized");

            setupToolbar();
            setupSpinners();
            setupDatePickers();
            setupClickListeners();
            
            // Initialize API service
            apiService = new ApiService();

            Log.d(TAG, "Activity setup completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            e.printStackTrace();

            // Show error message and finish
            Toast.makeText(this, "Error loading Add Promotion screen: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etPromotionCode = findViewById(R.id.et_promotion_code);
        etPromotionName = findViewById(R.id.et_promotion_name);
        etPromotionValue = findViewById(R.id.et_promotion_value);
        etBuyQuantity = findViewById(R.id.et_buy_quantity);
        etGetQuantity = findViewById(R.id.et_get_quantity);
        etStartDate = findViewById(R.id.et_start_date);
        etEndDate = findViewById(R.id.et_end_date);
        etUsageLimit = findViewById(R.id.et_usage_limit);

        spinnerPromotionType = findViewById(R.id.spinner_promotion_type);
        spinnerApplicableProducts = findViewById(R.id.spinner_applicable_products);
        spinnerStatus = findViewById(R.id.spinner_status);

        layoutPromotionValue = findViewById(R.id.layout_promotion_value);
        layoutBuyXGetY = findViewById(R.id.layout_buy_x_get_y); // Fixed: Uncommented and cast to LinearLayout

        btnCancel = findViewById(R.id.btn_cancel);
        btnSave = findViewById(R.id.btn_save);

        // Log any null views for debugging
        if (layoutBuyXGetY == null) {
            Log.w(TAG, "layoutBuyXGetY is null - check if layout_buy_x_get_y exists in XML");
        }
        if (layoutPromotionValue == null) {
            Log.w(TAG, "layoutPromotionValue is null - check if layout_promotion_value exists in XML");
        }
    }

    private void setupToolbar() {
        if (toolbar != null) {
            setSupportActionBar(toolbar);
            if (getSupportActionBar() != null) {
                getSupportActionBar().setDisplayHomeAsUpEnabled(true);
                getSupportActionBar().setTitle("Add Promotion");
            }
            toolbar.setNavigationOnClickListener(v -> onBackPressed());
        }
    }

    private void setupSpinners() {
        // Promotion Type
        String[] promotionTypes = {"Percentage", "Fixed Amount", "Buy X Get Y"};
        ArrayAdapter<String> typeAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, promotionTypes);
        if (spinnerPromotionType != null) {
            spinnerPromotionType.setAdapter(typeAdapter);

            spinnerPromotionType.setOnItemClickListener((parent, view, position, id) -> {
                String selectedType = promotionTypes[position];
                handlePromotionTypeSelection(selectedType);
            });
        }

        // Applicable Products
        String[] products = {"All Products", "Coffee", "Tea", "Pastries", "Hot Drinks", "Cold Drinks", "Sandwiches"};
        ArrayAdapter<String> productsAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, products);
        if (spinnerApplicableProducts != null) {
            spinnerApplicableProducts.setAdapter(productsAdapter);
        }

        // Status
        String[] statuses = {"Active", "Inactive"};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, statuses);
        if (spinnerStatus != null) {
            spinnerStatus.setAdapter(statusAdapter);
            spinnerStatus.setText("Active", false); // Default to Active
        }
    }

    private void handlePromotionTypeSelection(String type) {
        try {
            switch (type) {
                case "Percentage":
                    if (layoutPromotionValue != null) {
                        layoutPromotionValue.setHint("Percentage (e.g., 20)");
                        layoutPromotionValue.setVisibility(View.VISIBLE);
                    }
                    if (layoutBuyXGetY != null) {
                        layoutBuyXGetY.setVisibility(View.GONE);
                    }
                    break;
                case "Fixed Amount":
                    if (layoutPromotionValue != null) {
                        layoutPromotionValue.setHint("Fixed Amount (e.g., 5.00)");
                        layoutPromotionValue.setVisibility(View.VISIBLE);
                    }
                    if (layoutBuyXGetY != null) {
                        layoutBuyXGetY.setVisibility(View.GONE);
                    }
                    break;
                case "Buy X Get Y":
                    if (layoutPromotionValue != null) {
                        layoutPromotionValue.setVisibility(View.GONE);
                    }
                    if (layoutBuyXGetY != null) {
                        layoutBuyXGetY.setVisibility(View.VISIBLE);
                    }
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling promotion type selection", e);
        }
    }

    private void setupDatePickers() {
        if (etStartDate != null) {
            etStartDate.setOnClickListener(v -> showDatePicker(true));
        }
        if (etEndDate != null) {
            etEndDate.setOnClickListener(v -> showDatePicker(false));
        }
    }

    private void showDatePicker(boolean isStartDate) {
        Calendar calendar = isStartDate ? startCalendar : endCalendar;

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    String formattedDate = dateFormat.format(calendar.getTime());

                    if (isStartDate && etStartDate != null) {
                        etStartDate.setText(formattedDate);
                    } else if (!isStartDate && etEndDate != null) {
                        etEndDate.setText(formattedDate);
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        if (isStartDate) {
            datePickerDialog.getDatePicker().setMinDate(System.currentTimeMillis());
        } else {
            datePickerDialog.getDatePicker().setMinDate(startCalendar.getTimeInMillis());
        }

        datePickerDialog.show();
    }

    private void setupClickListeners() {
        if (btnCancel != null) {
            btnCancel.setOnClickListener(v -> onBackPressed());
        }
        if (btnSave != null) {
            btnSave.setOnClickListener(v -> savePromotion());
        }
    }

    private void savePromotion() {
        try {
            if (!validateInputs()) {
                return;
            }

            // Disable button to prevent double submission
            btnSave.setEnabled(false);
            btnSave.setText("Creating...");

            // Get form data
            String title = etPromotionName != null ? etPromotionName.getText().toString().trim() : "";
            String description = "Promotion: " + title; // Simple description, could be enhanced
            String promoCode = etPromotionCode != null ? etPromotionCode.getText().toString().trim().toUpperCase() : null;
            String type = spinnerPromotionType != null ? spinnerPromotionType.getText().toString() : "";
            String startDate = etStartDate != null ? etStartDate.getText().toString() : "";
            String endDate = etEndDate != null ? etEndDate.getText().toString() : "";
            String status = spinnerStatus != null ? spinnerStatus.getText().toString() : "";
            String applicableProducts = spinnerApplicableProducts != null ? spinnerApplicableProducts.getText().toString() : null;
            
            // Convert type and value to our API format
            String discountType;
            double discountValue = 0.0;
            
            switch (type) {
                case "Percentage":
                    discountType = "percentage";
                    try {
                        String percentValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "0";
                        discountValue = Double.parseDouble(percentValue);
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Error parsing percentage value", e);
                    }
                    break;
                case "Fixed Amount":
                    discountType = "fixed_amount";
                    try {
                        String fixedValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "0";
                        discountValue = Double.parseDouble(fixedValue);
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Error parsing fixed amount value", e);
                    }
                    break;
                case "Buy X Get Y":
                    discountType = "free_shipping"; // Using free_shipping as closest equivalent
                    discountValue = 0.0; // No monetary value for Buy X Get Y
                    break;
                default:
                    discountType = "percentage";
                    break;
            }
            
            // Get usage limit
            Integer usageLimit = null;
            try {
                String usageLimitText = etUsageLimit != null ? etUsageLimit.getText().toString() : "";
                if (!usageLimitText.isEmpty()) {
                    usageLimit = Integer.parseInt(usageLimitText);
                }
            } catch (NumberFormatException e) {
                Log.e(TAG, "Error parsing usage limit", e);
            }
            
            // Convert status to boolean
            boolean isActive = "Active".equals(status);
            
            // Set minimum order amount (could be enhanced with UI field)
            double minimumOrderAmount = 0.0;

            // Call API to create promotion
            apiService.createPromotion(title, description, discountType, discountValue, 
                    minimumOrderAmount, startDate, endDate, isActive, usageLimit, 
                    applicableProducts, promoCode, new ApiService.PromotionActionCallback() {
                @Override
                public void onSuccess(JSONObject result) {
                    // Reset button state
                    btnSave.setEnabled(true);
                    btnSave.setText("Save");
                    
                    Toast.makeText(AddPromotionActivity.this, 
                            "Promotion '" + title + "' created successfully!", 
                            Toast.LENGTH_SHORT).show();
                    finish();
                }

                @Override
                public void onError(String error) {
                    // Reset button state
                    btnSave.setEnabled(true);
                    btnSave.setText("Save");
                    
                    Toast.makeText(AddPromotionActivity.this, 
                            "Error creating promotion: " + error, 
                            Toast.LENGTH_LONG).show();
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error saving promotion", e);
            
            // Reset button state
            btnSave.setEnabled(true);
            btnSave.setText("Save");
            
            Toast.makeText(this, "Error saving promotion", Toast.LENGTH_SHORT).show();
        }
    }

    private String getPromotionValue() {
        try {
            String type = spinnerPromotionType != null ? spinnerPromotionType.getText().toString() : "";

            switch (type) {
                case "Percentage":
                    String percentValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "";
                    return percentValue + "%";
                case "Fixed Amount":
                    String fixedValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "";
                    return "$" + fixedValue;
                case "Buy X Get Y":
                    String buyQty = etBuyQuantity != null ? etBuyQuantity.getText().toString() : "";
                    String getQty = etGetQuantity != null ? etGetQuantity.getText().toString() : "";
                    return "Buy " + buyQty + " Get " + getQty;
                default:
                    return etPromotionValue != null ? etPromotionValue.getText().toString() : "";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting promotion value", e);
            return "";
        }
    }

    private boolean validateInputs() {
        try {
            // Promotion Code
            if (etPromotionCode == null || etPromotionCode.getText().toString().trim().isEmpty()) {
                if (etPromotionCode != null) {
                    etPromotionCode.setError("Promotion code is required");
                    etPromotionCode.requestFocus();
                }
                return false;
            }

            // Promotion Name
            if (etPromotionName == null || etPromotionName.getText().toString().trim().isEmpty()) {
                if (etPromotionName != null) {
                    etPromotionName.setError("Promotion name is required");
                    etPromotionName.requestFocus();
                }
                return false;
            }

            // Promotion Type
            if (spinnerPromotionType == null || spinnerPromotionType.getText().toString().isEmpty()) {
                Toast.makeText(this, "Please select a promotion type", Toast.LENGTH_SHORT).show();
                return false;
            }

            // Type-specific validation
            String type = spinnerPromotionType.getText().toString();
            switch (type) {
                case "Percentage":
                case "Fixed Amount":
                    if (etPromotionValue == null || etPromotionValue.getText().toString().trim().isEmpty()) {
                        if (etPromotionValue != null) {
                            etPromotionValue.setError("Promotion value is required");
                            etPromotionValue.requestFocus();
                        }
                        return false;
                    }
                    break;
                case "Buy X Get Y":
                    if (etBuyQuantity == null || etBuyQuantity.getText().toString().trim().isEmpty()) {
                        if (etBuyQuantity != null) {
                            etBuyQuantity.setError("Buy quantity is required");
                            etBuyQuantity.requestFocus();
                        }
                        return false;
                    }
                    if (etGetQuantity == null || etGetQuantity.getText().toString().trim().isEmpty()) {
                        if (etGetQuantity != null) {
                            etGetQuantity.setError("Get quantity is required");
                            etGetQuantity.requestFocus();
                        }
                        return false;
                    }
                    break;
            }

            // Date validation
            if (etStartDate == null || etStartDate.getText().toString().isEmpty()) {
                Toast.makeText(this, "Start date is required", Toast.LENGTH_SHORT).show();
                return false;
            }

            if (etEndDate == null || etEndDate.getText().toString().isEmpty()) {
                Toast.makeText(this, "End date is required", Toast.LENGTH_SHORT).show();
                return false;
            }

            if (endCalendar.before(startCalendar)) {
                Toast.makeText(this, "End date must be after start date", Toast.LENGTH_SHORT).show();
                return false;
            }

            // Applicable Products
            if (spinnerApplicableProducts == null || spinnerApplicableProducts.getText().toString().isEmpty()) {
                Toast.makeText(this, "Please select applicable products", Toast.LENGTH_SHORT).show();
                return false;
            }

            // Status
            if (spinnerStatus == null || spinnerStatus.getText().toString().isEmpty()) {
                Toast.makeText(this, "Please select a status", Toast.LENGTH_SHORT).show();
                return false;
            }

            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error validating inputs", e);
            Toast.makeText(this, "Validation error", Toast.LENGTH_SHORT).show();
            return false;
        }
    }
}