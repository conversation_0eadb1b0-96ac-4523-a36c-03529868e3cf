package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.textfield.TextInputEditText;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.MenuItemManagementAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.models.MenuItem;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class MenuItemManagementActivity extends AppCompatActivity implements MenuItemManagementAdapter.OnMenuItemActionListener {

    private Toolbar toolbar;
    private TextInputEditText etSearch;
    private ChipGroup chipGroupCategories;
    private ChipGroup chipGroupStatus;
    private MaterialButton btnAddMenuItem;
    private MaterialButton btnSort;
    private RecyclerView rvMenuItems;
    private View layoutEmptyState;

    // Category filter chips
    private Chip chipAllCategories, chipHotCoffee, chipColdCoffee, chipTea, chipSnacks, chipDesserts;
    // Status filter chips
    private Chip chipAllStatus, chipAvailable, chipUnavailable, chipActive, chipInactive;

    private MenuItemManagementAdapter menuItemAdapter;
    private List<MenuItem> allMenuItems;
    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        try {
            setContentView(R.layout.activity_menu_item_management);
            allMenuItems = new ArrayList<>();

            initViews();
            setupToolbar();
            setupRecyclerView();
            setupClickListeners();
            setupSearchFilter();
            setupChipFilters();

            // Initialize API service
            apiService = new ApiService();

            loadMenuItems();
        } catch (Exception e) {
            android.util.Log.e("MenuManagement", "Error in onCreate", e);
            // Show error message and finish activity
            android.widget.Toast.makeText(this, "Error loading menu management: " + e.getMessage(), android.widget.Toast.LENGTH_LONG).show();
            finish();
        }
    }

    private void initViews() {
        try {
            toolbar = findViewById(R.id.toolbar);
            etSearch = findViewById(R.id.et_search);
            chipGroupCategories = findViewById(R.id.chip_group_categories);
            chipGroupStatus = findViewById(R.id.chip_group_status);
            btnAddMenuItem = findViewById(R.id.btn_add_menu_item);
            btnSort = findViewById(R.id.btn_sort);
            rvMenuItems = findViewById(R.id.rv_menu_items);
            layoutEmptyState = findViewById(R.id.layout_empty_state);

            // Initialize category filter chips
            chipAllCategories = findViewById(R.id.chip_all_categories);
            chipHotCoffee = findViewById(R.id.chip_hot_coffee);
            chipColdCoffee = findViewById(R.id.chip_cold_coffee);
            chipTea = findViewById(R.id.chip_tea);
            chipSnacks = findViewById(R.id.chip_snacks);
            chipDesserts = findViewById(R.id.chip_desserts);

            // Initialize status filter chips
            chipAllStatus = findViewById(R.id.chip_all_status);
            chipAvailable = findViewById(R.id.chip_available);
            chipUnavailable = findViewById(R.id.chip_unavailable);
            chipActive = findViewById(R.id.chip_active);
            chipInactive = findViewById(R.id.chip_inactive);
            
            // Check if any critical views are null
            if (toolbar == null || etSearch == null || rvMenuItems == null || layoutEmptyState == null) {
                throw new RuntimeException("Critical views not found in layout");
            }
            
            android.util.Log.d("MenuManagement", "All views initialized successfully");
        } catch (Exception e) {
            android.util.Log.e("MenuManagement", "Error initializing views", e);
            throw e;
        }
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Menu Management");
        }
        toolbar.setNavigationOnClickListener(v -> getOnBackPressedDispatcher().onBackPressed());
    }

    private void setupRecyclerView() {
        rvMenuItems.setLayoutManager(new LinearLayoutManager(this));
        menuItemAdapter = new MenuItemManagementAdapter(this, this);
        rvMenuItems.setAdapter(menuItemAdapter);

        android.util.Log.d("MenuManagement", "RecyclerView setup complete with adapter and listener");
    }

    private void setupClickListeners() {
        btnAddMenuItem.setOnClickListener(v -> openAddMenuItemActivity());
        btnSort.setOnClickListener(v -> showSortDialog());
    }

    private void setupSearchFilter() {
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                filterMenuItems();
            }
        });
    }

    private void setupChipFilters() {
        // Category filter chips
        chipGroupCategories.setOnCheckedStateChangeListener((group, checkedIds) -> {
            handleCategoryChipSelection(checkedIds);
            filterMenuItems();
        });

        // Status filter chips
        chipGroupStatus.setOnCheckedStateChangeListener((group, checkedIds) -> {
            handleStatusChipSelection(checkedIds);
            filterMenuItems();
        });
    }

    private void handleCategoryChipSelection(List<Integer> checkedIds) {
        boolean allCategoriesSelected = checkedIds.contains(R.id.chip_all_categories);
        boolean anySpecificCategorySelected = checkedIds.contains(R.id.chip_hot_coffee) ||
                checkedIds.contains(R.id.chip_cold_coffee) ||
                checkedIds.contains(R.id.chip_tea) ||
                checkedIds.contains(R.id.chip_snacks) ||
                checkedIds.contains(R.id.chip_desserts);

        if (allCategoriesSelected && anySpecificCategorySelected) {
            chipHotCoffee.setChecked(false);
            chipColdCoffee.setChecked(false);
            chipTea.setChecked(false);
            chipSnacks.setChecked(false);
            chipDesserts.setChecked(false);
        } else if (!allCategoriesSelected && anySpecificCategorySelected && chipAllCategories.isChecked()) {
            chipAllCategories.setChecked(false);
        } else if (checkedIds.isEmpty()) {
            chipAllCategories.setChecked(true);
        }
    }

    private void handleStatusChipSelection(List<Integer> checkedIds) {
        boolean allStatusSelected = checkedIds.contains(R.id.chip_all_status);
        boolean anySpecificStatusSelected = checkedIds.contains(R.id.chip_available) ||
                checkedIds.contains(R.id.chip_unavailable) ||
                checkedIds.contains(R.id.chip_active) ||
                checkedIds.contains(R.id.chip_inactive);

        if (allStatusSelected && anySpecificStatusSelected) {
            chipAvailable.setChecked(false);
            chipUnavailable.setChecked(false);
            chipActive.setChecked(false);
            chipInactive.setChecked(false);
        } else if (!allStatusSelected && anySpecificStatusSelected && chipAllStatus.isChecked()) {
            chipAllStatus.setChecked(false);
        } else if (checkedIds.isEmpty()) {
            chipAllStatus.setChecked(true);
        }
    }

    private void loadMenuItems() {
        try {
            // Initialize empty list and show loading state
            allMenuItems = new ArrayList<>();
            showEmptyState();

            android.util.Log.d("MenuManagement", "Starting to load menu items from API...");

            if (apiService == null) {
                android.util.Log.e("MenuManagement", "ApiService is null, reinitializing...");
                apiService = new ApiService();
            }

            apiService.getMenuItems(new ApiService.MenuItemsCallback() {
                @Override
                public void onSuccess(JSONArray menuItems) {
                    try {
                        android.util.Log.d("MenuManagement", "API response received with " + menuItems.length() + " items");
                        
                        allMenuItems = new ArrayList<>();

                        for (int i = 0; i < menuItems.length(); i++) {
                            try {
                                JSONObject menuItemJson = menuItems.getJSONObject(i);
                                android.util.Log.d("MenuManagement", "Parsing menu item " + i + ": " + menuItemJson.toString());
                                MenuItem menuItem = new MenuItem(menuItemJson);
                                allMenuItems.add(menuItem);
                                android.util.Log.d("MenuManagement", "Successfully added menu item: " + menuItem.getName() + " (ID: " + menuItem.getMenuItemId() + ")");
                            } catch (Exception e) {
                                android.util.Log.e("MenuManagement", "Error parsing menu item at index " + i + ": " + e.getMessage(), e);
                                // Try to log the problematic JSON for debugging
                                try {
                                    JSONObject problemJson = menuItems.getJSONObject(i);
                                    android.util.Log.e("MenuManagement", "Problematic JSON: " + problemJson.toString());
                                } catch (Exception ex) {
                                    android.util.Log.e("MenuManagement", "Could not retrieve problematic JSON", ex);
                                }
                            }
                        }

                        android.util.Log.d("MenuManagement", "Successfully loaded " + allMenuItems.size() + " menu items from API");
                        
                        runOnUiThread(() -> {
                            try {
                                hideEmptyState();
                                filterMenuItems();
                            } catch (Exception e) {
                                android.util.Log.e("MenuManagement", "Error updating UI after loading menu items", e);
                                showEmptyState();
                            }
                        });
                    } catch (Exception e) {
                        android.util.Log.e("MenuManagement", "Error processing menu items response", e);
                        runOnUiThread(() -> {
                            hideEmptyState();
                            android.widget.Toast.makeText(MenuItemManagementActivity.this,
                                    "Error processing menu items: " + e.getMessage(),
                                    android.widget.Toast.LENGTH_LONG).show();
                            loadMockDataAsFallback();
                        });
                    }
                }

                @Override
                public void onError(String error) {
                    android.util.Log.e("MenuManagement", "Error loading menu items: " + error);
                    
                    runOnUiThread(() -> {
                        hideEmptyState();
                        android.widget.Toast.makeText(MenuItemManagementActivity.this,
                                "Error loading menu items: " + error,
                                android.widget.Toast.LENGTH_LONG).show();

                        // Load mock data as fallback
                        loadMockDataAsFallback();
                    });
                }
            });
        } catch (Exception e) {
            android.util.Log.e("MenuManagement", "Error in loadMenuItems method", e);
            runOnUiThread(() -> {
                hideEmptyState();
                android.widget.Toast.makeText(this,
                        "Failed to load menu items: " + e.getMessage(),
                        android.widget.Toast.LENGTH_LONG).show();
                loadMockDataAsFallback();
            });
        }
    }

    private void loadMockDataAsFallback() {
        android.util.Log.d("MenuManagement", "Loading mock data as fallback...");
        
        allMenuItems = new ArrayList<>();

        // Add mock menu items with all required parameters
        allMenuItems.add(new MenuItem(1, "Espresso", "Strong black coffee", 3.50, "Hot Coffee",
                "https://example.com/espresso.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(2, "Cappuccino", "Espresso with steamed milk and foam", 4.25, "Hot Coffee",
                "https://example.com/cappuccino.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(3, "Iced Latte", "Cold espresso with milk over ice", 4.75, "Cold Coffee",
                "https://example.com/iced_latte.jpg", false, "inactive", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(4, "Green Tea", "Traditional green tea", 2.50, "Tea",
                "https://example.com/green_tea.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(5, "Earl Grey", "Classic black tea with bergamot", 2.75, "Tea",
                "https://example.com/earl_grey.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(6, "Chocolate Croissant", "Buttery pastry with chocolate", 3.25, "Desserts",
                "https://example.com/croissant.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(7, "Blueberry Muffin", "Fresh baked muffin with blueberries", 2.95, "Desserts",
                "https://example.com/muffin.jpg", false, "out_of_stock", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(8, "Turkey Sandwich", "Turkey with lettuce and tomato", 7.50, "Snacks",
                "https://example.com/sandwich.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(9, "Frappuccino", "Blended coffee drink with whipped cream", 5.25, "Cold Coffee",
                "https://example.com/frappuccino.jpg", true, "inactive", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(10, "Bagel with Cream Cheese", "Fresh bagel with cream cheese", 3.75, "Snacks",
                "https://example.com/bagel.jpg", false, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(11, "Americano", "Espresso with hot water", 3.00, "Hot Coffee",
                "https://example.com/americano.jpg", true, "active", "2024-01-01", "2024-01-01"));
        allMenuItems.add(new MenuItem(12, "Iced Tea", "Refreshing cold tea", 2.25, "Tea",
                "https://example.com/iced_tea.jpg", true, "active", "2024-01-01", "2024-01-01"));

        android.util.Log.d("MenuManagement", "Loaded " + allMenuItems.size() + " mock menu items as fallback");
        filterMenuItems();
        hideEmptyState();
    }

    private void filterMenuItems() {
        if (etSearch.getText() == null) return;

        String searchQuery = etSearch.getText().toString().trim().toLowerCase();

        List<MenuItem> filteredMenuItems = new ArrayList<>();

        for (MenuItem menuItem : allMenuItems) {
            // Search filter
            boolean matchesSearch = searchQuery.isEmpty() ||
                    menuItem.getName().toLowerCase().contains(searchQuery) ||
                    (menuItem.getDescription() != null && menuItem.getDescription().toLowerCase().contains(searchQuery)) ||
                    menuItem.getCategory().toLowerCase().contains(searchQuery);

            // Category filter - map API categories to our filter chips
            boolean matchesCategory = chipAllCategories.isChecked() ||
                    (chipHotCoffee.isChecked() && ("Espresso".equalsIgnoreCase(menuItem.getCategory()) || 
                                                   "Milk Coffee".equalsIgnoreCase(menuItem.getCategory()) ||
                                                   "Specialty".equalsIgnoreCase(menuItem.getCategory()))) ||
                    (chipColdCoffee.isChecked() && ("Cold Drinks".equalsIgnoreCase(menuItem.getCategory()) ||
                                                     "Cold Coffee".equalsIgnoreCase(menuItem.getCategory()))) ||
                    (chipTea.isChecked() && "Tea".equalsIgnoreCase(menuItem.getCategory())) ||
                    (chipSnacks.isChecked() && "Snacks".equalsIgnoreCase(menuItem.getCategory())) ||
                    (chipDesserts.isChecked() && ("Pastries".equalsIgnoreCase(menuItem.getCategory()) ||
                                                   "Desserts".equalsIgnoreCase(menuItem.getCategory())));

            // Status filter
            boolean matchesStatus = chipAllStatus.isChecked() ||
                    (chipAvailable.isChecked() && menuItem.isAvailable()) ||
                    (chipUnavailable.isChecked() && !menuItem.isAvailable()) ||
                    (chipActive.isChecked() && "active".equalsIgnoreCase(menuItem.getStatus())) ||
                    (chipInactive.isChecked() && "inactive".equalsIgnoreCase(menuItem.getStatus()));

            if (matchesSearch && matchesCategory && matchesStatus) {
                filteredMenuItems.add(menuItem);
            }
        }

        menuItemAdapter.updateList(filteredMenuItems);

        if (filteredMenuItems.isEmpty()) {
            showEmptyState();
        } else {
            hideEmptyState();
        }
    }

    private void openAddMenuItemActivity() {
        Intent intent = new Intent(this, AddMenuItemActivity.class);
        startActivity(intent);
    }

    private void showSortDialog() {
        String[] sortOptions = {"Name A-Z", "Name Z-A", "Price Low-High", "Price High-Low", "Category", "Status", "Availability"};

        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Sort By")
                .setItems(sortOptions, (dialog, which) -> sortMenuItems(which))
                .show();
    }

    private void sortMenuItems(int sortType) {
        List<MenuItem> currentList = new ArrayList<>(allMenuItems);

        switch (sortType) {
            case 0: // Name A-Z
                currentList.sort((a, b) -> a.getName().compareToIgnoreCase(b.getName()));
                break;
            case 1: // Name Z-A
                currentList.sort((a, b) -> b.getName().compareToIgnoreCase(a.getName()));
                break;
            case 2: // Price Low-High
                currentList.sort(Comparator.comparingDouble(MenuItem::getPrice));
                break;
            case 3: // Price High-Low
                currentList.sort(Comparator.comparingDouble(MenuItem::getPrice).reversed());
                break;
            case 4: // Category
                currentList.sort((a, b) -> a.getCategory().compareToIgnoreCase(b.getCategory()));
                break;
            case 5: // Status
                currentList.sort((a, b) -> a.getStatus().compareToIgnoreCase(b.getStatus()));
                break;
            case 6: // Availability
                currentList.sort((a, b) -> Boolean.compare(b.isAvailable(), a.isAvailable()));
                break;
        }

        allMenuItems = currentList;
        filterMenuItems();
    }

    private void showEmptyState() {
        rvMenuItems.setVisibility(View.GONE);
        layoutEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        rvMenuItems.setVisibility(View.VISIBLE);
        layoutEmptyState.setVisibility(View.GONE);
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadMenuItems();
    }

    // Implement MenuItemManagementAdapter interface methods
    @Override
    public void onMenuItemDeleted(MenuItem menuItem, int position) {
        apiService.deleteMenuItem(menuItem.getId(), new ApiService.MenuItemActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                allMenuItems.remove(menuItem);
                filterMenuItems();
                android.widget.Toast.makeText(MenuItemManagementActivity.this,
                        "Menu item deleted successfully",
                        android.widget.Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                android.widget.Toast.makeText(MenuItemManagementActivity.this,
                        "Error deleting menu item: " + error,
                        android.widget.Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onMenuItemEdit(MenuItem menuItem) {
        Intent intent = new Intent(this, EditMenuItemActivity.class);
        intent.putExtra("menu_item_id", menuItem.getId());
        intent.putExtra("name", menuItem.getName());
        intent.putExtra("description", menuItem.getDescription());
        intent.putExtra("price", menuItem.getPrice());
        intent.putExtra("category", menuItem.getCategory());
        intent.putExtra("image_url", menuItem.getImageUrl());
        intent.putExtra("available", menuItem.isAvailable());
        intent.putExtra("status", menuItem.getStatus());
        intent.putExtra("edit_mode", true);
        startActivity(intent);
    }

    @Override
    public void onMenuItemAvailabilityToggled(MenuItem menuItem, int position) {
        boolean newAvailability = !menuItem.isAvailable();

        apiService.toggleMenuItemAvailability(menuItem.getId(), newAvailability, new ApiService.MenuItemActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                if (allMenuItems != null) {
                    for (MenuItem item : allMenuItems) {
                        if (item.getId() == menuItem.getId()) {
                            item.setAvailable(newAvailability);
                            break;
                        }
                    }
                }
                filterMenuItems();

                android.widget.Toast.makeText(MenuItemManagementActivity.this,
                        "Menu item " + (newAvailability ? "made available" : "made unavailable"),
                        android.widget.Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                android.widget.Toast.makeText(MenuItemManagementActivity.this,
                        "Error updating availability: " + error,
                        android.widget.Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onMenuItemStatusToggled(MenuItem menuItem, int position) {
        String newStatus = "active".equals(menuItem.getStatus()) ? "inactive" : "active";

        apiService.updateMenuItemStatus(menuItem.getId(), newStatus, new ApiService.MenuItemActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                if (allMenuItems != null) {
                    for (MenuItem item : allMenuItems) {
                        if (item.getId() == menuItem.getId()) {
                            item.setStatus(newStatus);
                            break;
                        }
                    }
                }
                filterMenuItems();

                android.widget.Toast.makeText(MenuItemManagementActivity.this,
                        "Menu item status updated to " + newStatus,
                        android.widget.Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                android.widget.Toast.makeText(MenuItemManagementActivity.this,
                        "Error updating status: " + error,
                        android.widget.Toast.LENGTH_SHORT).show();
            }
        });
    }
}
