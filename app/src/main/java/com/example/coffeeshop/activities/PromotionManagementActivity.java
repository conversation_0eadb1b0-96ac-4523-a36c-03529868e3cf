package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.google.android.material.chip.ChipGroup;
import com.google.android.material.textfield.TextInputEditText;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.PromotionAdapter;
import com.example.coffeeshop.api.ApiService;
import com.example.coffeeshop.models.Promotion;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PromotionManagementActivity extends AppCompatActivity implements PromotionAdapter.OnPromotionActionListener {

    private Toolbar toolbar;
    private TextInputEditText etSearch;
    private ChipGroup chipGroupTypes;
    private ChipGroup chipGroupStatus;
    private MaterialButton btnAddPromotion;
    private MaterialButton btnSort;
    private RecyclerView rvPromotions;
    private View layoutEmptyState;

    // Type filter chips
    private Chip chipAllTypes, chipPercentage, chipFixedAmount, chipBuyXGetY;
    // Status filter chips
    private Chip chipAllStatus, chipActive, chipInactive, chipExpired;

    private PromotionAdapter promotionAdapter;
    private List<Promotion> allPromotions;
    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_promotion_management);

        initViews();
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        setupSearchFilter();
        setupChipFilters();
        
        // Initialize API service
        apiService = new ApiService();
        loadPromotions();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etSearch = findViewById(R.id.et_search);
        chipGroupTypes = findViewById(R.id.chip_group_types);
        chipGroupStatus = findViewById(R.id.chip_group_status);
        btnAddPromotion = findViewById(R.id.btn_add_promotion);
        btnSort = findViewById(R.id.btn_sort);
        rvPromotions = findViewById(R.id.rv_promotions);
        layoutEmptyState = findViewById(R.id.layout_empty_state);

        // Initialize type filter chips
        chipAllTypes = findViewById(R.id.chip_all_types);
        chipPercentage = findViewById(R.id.chip_percentage);
        chipFixedAmount = findViewById(R.id.chip_fixed_amount);
        chipBuyXGetY = findViewById(R.id.chip_buy_x_get_y);

        // Initialize status filter chips
        chipAllStatus = findViewById(R.id.chip_all_status);
        chipActive = findViewById(R.id.chip_active);
        chipInactive = findViewById(R.id.chip_inactive);
        chipExpired = findViewById(R.id.chip_expired);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Promotion Management");
        }
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void setupRecyclerView() {
        rvPromotions.setLayoutManager(new LinearLayoutManager(this));
        promotionAdapter = new PromotionAdapter(this, this);
        rvPromotions.setAdapter(promotionAdapter);
        
        android.util.Log.d("PromotionManagement", "RecyclerView setup complete with adapter and listener");
    }

    private void setupClickListeners() {
        btnAddPromotion.setOnClickListener(v -> openAddPromotionActivity());
        btnSort.setOnClickListener(v -> showSortDialog());
    }

    private void setupSearchFilter() {
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                filterPromotions();
            }
        });
    }

    private void setupChipFilters() {
        // Type filter chips - handle mutual exclusivity with "All Types"
        chipGroupTypes.setOnCheckedStateChangeListener((group, checkedIds) -> {
            handleTypeChipSelection(checkedIds);
            filterPromotions();
        });

        // Status filter chips - handle mutual exclusivity with "All Status"
        chipGroupStatus.setOnCheckedStateChangeListener((group, checkedIds) -> {
            handleStatusChipSelection(checkedIds);
            filterPromotions();
        });
    }

    private void handleTypeChipSelection(List<Integer> checkedIds) {
        boolean allTypesSelected = checkedIds.contains(R.id.chip_all_types);
        boolean anySpecificTypeSelected = checkedIds.contains(R.id.chip_percentage) ||
                checkedIds.contains(R.id.chip_fixed_amount) ||
                checkedIds.contains(R.id.chip_buy_x_get_y);

        // If "All Types" is clicked while specific types are selected
        if (allTypesSelected && anySpecificTypeSelected) {
            chipPercentage.setChecked(false);
            chipFixedAmount.setChecked(false);
            chipBuyXGetY.setChecked(false);
        }
        // If any specific type is clicked while "All Types" is selected
        else if (!allTypesSelected && anySpecificTypeSelected && chipAllTypes.isChecked()) {
            chipAllTypes.setChecked(false);
        }
        // If no chips are selected, default to "All Types"
        else if (checkedIds.isEmpty()) {
            chipAllTypes.setChecked(true);
        }
    }

    private void handleStatusChipSelection(List<Integer> checkedIds) {
        boolean allStatusSelected = checkedIds.contains(R.id.chip_all_status);
        boolean anySpecificStatusSelected = checkedIds.contains(R.id.chip_active) ||
                checkedIds.contains(R.id.chip_inactive) ||
                checkedIds.contains(R.id.chip_expired);

        // If "All Status" is clicked while specific statuses are selected
        if (allStatusSelected && anySpecificStatusSelected) {
            chipActive.setChecked(false);
            chipInactive.setChecked(false);
            chipExpired.setChecked(false);
        }
        // If any specific status is clicked while "All Status" is selected
        else if (!allStatusSelected && anySpecificStatusSelected && chipAllStatus.isChecked()) {
            chipAllStatus.setChecked(false);
        }
        // If no chips are selected, default to "All Status"
        else if (checkedIds.isEmpty()) {
            chipAllStatus.setChecked(true);
        }
    }

    private void loadPromotions() {
        // Show loading state
        showEmptyState();
        
        apiService.getPromotions(null, null, null, new ApiService.PromotionsCallback() {
            @Override
            public void onSuccess(JSONArray promotions) {
                allPromotions = new ArrayList<>();
                
                for (int i = 0; i < promotions.length(); i++) {
                    try {
                        JSONObject promotionJson = promotions.getJSONObject(i);
                        Promotion promotion = new Promotion(promotionJson);
                        allPromotions.add(promotion);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                
                filterPromotions(); // Apply initial filters
            }

            @Override
            public void onError(String error) {
                // Hide loading and show empty state with error
                hideEmptyState();
                // You could show a toast or dialog here
                android.widget.Toast.makeText(PromotionManagementActivity.this, 
                    "Error loading promotions: " + error, 
                    android.widget.Toast.LENGTH_SHORT).show();
                
                // Fallback to empty list
                allPromotions = new ArrayList<>();
                filterPromotions();
            }
        });
    }

    private void filterPromotions() {
        String searchQuery = etSearch.getText().toString().trim().toLowerCase();

        // Get selected type filters
        boolean showAllTypes = chipAllTypes.isChecked();
        boolean showPercentage = chipPercentage.isChecked();
        boolean showFixedAmount = chipFixedAmount.isChecked();
        boolean showBuyXGetY = chipBuyXGetY.isChecked();

        // Get selected status filters
        boolean showAllStatus = chipAllStatus.isChecked();
        boolean showActive = chipActive.isChecked();
        boolean showInactive = chipInactive.isChecked();
        boolean showExpired = chipExpired.isChecked();

        List<Promotion> filteredPromotions = new ArrayList<>();

        for (Promotion promotion : allPromotions) {
            // Search filter - check if promotion matches search query
            boolean matchesSearch = searchQuery.isEmpty() ||
                    (promotion.getPromoCode() != null && promotion.getPromoCode().toLowerCase().contains(searchQuery)) ||
                    promotion.getTitle().toLowerCase().contains(searchQuery) ||
                    promotion.getDescription().toLowerCase().contains(searchQuery) ||
                    (promotion.getApplicableProducts() != null && promotion.getApplicableProducts().toLowerCase().contains(searchQuery));

            // Type filter - OR logic for multiple selected types
            boolean matchesType = showAllTypes ||
                    (showPercentage && "percentage".equals(promotion.getDiscountType())) ||
                    (showFixedAmount && "fixed_amount".equals(promotion.getDiscountType())) ||
                    (showBuyXGetY && "free_shipping".equals(promotion.getDiscountType())); // Using free_shipping as Buy X Get Y equivalent

            // Status filter - OR logic for multiple selected statuses
            boolean matchesStatus = showAllStatus ||
                    (showActive && promotion.isActive()) ||
                    (showInactive && !promotion.isActive()) ||
                    (showExpired && !promotion.isActive()); // Simplified expired logic

            // Add promotion if it matches ALL filter criteria (AND logic between categories)
            if (matchesSearch && matchesType && matchesStatus) {
                filteredPromotions.add(promotion);
            }
        }

        promotionAdapter.updateList(filteredPromotions);

        if (filteredPromotions.isEmpty()) {
            showEmptyState();
        } else {
            hideEmptyState();
        }
    }

    private void openAddPromotionActivity() {
        Intent intent = new Intent(this, AddPromotionActivity.class);
        startActivity(intent);
    }

    private void showSortDialog() {
        String[] sortOptions = {"Name A-Z", "Name Z-A", "Code A-Z", "Type", "Status", "Start Date", "End Date", "Usage Count"};

        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("Sort By")
                .setItems(sortOptions, (dialog, which) -> sortPromotions(which))
                .show();
    }

    private void sortPromotions(int sortType) {
        List<Promotion> currentList = new ArrayList<>(allPromotions);

        switch (sortType) {
            case 0: // Name A-Z
                Collections.sort(currentList, (a, b) -> a.getTitle().compareToIgnoreCase(b.getTitle()));
                break;
            case 1: // Name Z-A
                Collections.sort(currentList, (a, b) -> b.getTitle().compareToIgnoreCase(a.getTitle()));
                break;
            case 2: // Code A-Z
                Collections.sort(currentList, (a, b) -> {
                    String codeA = a.getPromoCode() != null ? a.getPromoCode() : "";
                    String codeB = b.getPromoCode() != null ? b.getPromoCode() : "";
                    return codeA.compareToIgnoreCase(codeB);
                });
                break;
            case 3: // Type
                Collections.sort(currentList, (a, b) -> a.getDiscountType().compareToIgnoreCase(b.getDiscountType()));
                break;
            case 4: // Status
                Collections.sort(currentList, (a, b) -> Boolean.compare(b.isActive(), a.isActive()));
                break;
            case 5: // Start Date
                Collections.sort(currentList, (a, b) -> a.getStartDate().compareToIgnoreCase(b.getStartDate()));
                break;
            case 6: // End Date
                Collections.sort(currentList, (a, b) -> a.getEndDate().compareToIgnoreCase(b.getEndDate()));
                break;
            case 7: // Usage Count
                currentList.sort((a, b) -> Integer.compare(b.getCurrentUsage(), a.getCurrentUsage()));
                break;
        }

        allPromotions = currentList;
        filterPromotions(); // Reapply filters with sorted data
    }

    private void showEmptyState() {
        rvPromotions.setVisibility(View.GONE);
        layoutEmptyState.setVisibility(View.VISIBLE);
    }

    private void hideEmptyState() {
        rvPromotions.setVisibility(View.VISIBLE);
        layoutEmptyState.setVisibility(View.GONE);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Always refresh promotions when returning to this activity
        // This ensures new promotions added in AddPromotionActivity are displayed
        loadPromotions();
    }

    // Implement PromotionAdapter interface methods
    @Override
    public void onPromotionDeleted(Promotion promotion, int position) {
        // Call API to delete promotion
        apiService.deletePromotion(promotion.getPromotionId(), new ApiService.PromotionActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                allPromotions.remove(promotion);
                filterPromotions(); // Refresh the filtered list
                android.widget.Toast.makeText(PromotionManagementActivity.this, 
                    "Promotion deleted successfully", 
                    android.widget.Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                android.widget.Toast.makeText(PromotionManagementActivity.this, 
                    "Error deleting promotion: " + error, 
                    android.widget.Toast.LENGTH_SHORT).show();
            }
        });
    }

    @Override
    public void onPromotionEdit(Promotion promotion) {
        // Open EditPromotionActivity with promotion data
        Intent intent = new Intent(this, EditPromotionActivity.class);
        intent.putExtra("promotion_id", promotion.getPromotionId());
        intent.putExtra("title", promotion.getTitle());
        intent.putExtra("description", promotion.getDescription());
        intent.putExtra("discount_type", promotion.getDiscountType());
        intent.putExtra("discount_value", promotion.getDiscountValue());
        intent.putExtra("minimum_order_amount", promotion.getMinimumOrderAmount());
        intent.putExtra("start_date", promotion.getStartDate());
        intent.putExtra("end_date", promotion.getEndDate());
        intent.putExtra("is_active", promotion.isActive());
        intent.putExtra("usage_limit", promotion.getUsageLimit());
        intent.putExtra("applicable_products", promotion.getApplicableProducts());
        intent.putExtra("promo_code", promotion.getPromoCode());
        intent.putExtra("edit_mode", true);
        startActivity(intent);
    }

    @Override
    public void onPromotionStatusToggled(Promotion promotion, int position) {
        boolean newStatus = !promotion.isActive();
        android.util.Log.d("PromotionManagement", "Toggling promotion " + promotion.getPromotionId() + " from " + promotion.isActive() + " to " + newStatus);
        apiService.togglePromotionStatus(promotion.getPromotionId(), newStatus, new ApiService.PromotionActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                android.util.Log.d("PromotionManagement", "Successfully toggled promotion status");
                // Always update the main list
                if (allPromotions != null) {
                    for (Promotion p : allPromotions) {
                        if (p.getPromotionId() == promotion.getPromotionId()) {
                            p.setActive(newStatus);
                            break;
                        }
                    }
                }
                // Refresh the filtered list and adapter
                filterPromotions();

                android.widget.Toast.makeText(PromotionManagementActivity.this,
                    "Promotion " + (newStatus ? "activated" : "deactivated"),
                    android.widget.Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String error) {
                android.util.Log.e("PromotionManagement", "Error toggling promotion status: " + error);
                android.widget.Toast.makeText(PromotionManagementActivity.this,
                    "Error updating promotion status: " + error,
                    android.widget.Toast.LENGTH_SHORT).show();
            }
        });
    }

}