package com.example.coffeeshop.activities;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperProfile extends BaseActivity {

    private TextView tvName, tvEmail, tvPhone, tvRole;
    private Button btnLogout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_information);

        tvName = findViewById(R.id.et_full_name);
        tvEmail = findViewById(R.id.et_email);
        tvPhone = findViewById(R.id.et_phone);
        btnLogout = findViewById(R.id.btn_logout);

        loadUserData();
        setupBottomNav();
        btnLogout.setOnClickListener(v -> logout());
    }

    private void loadUserData() {
        SharedPreferences prefs = getSharedPreferences("user_data", MODE_PRIVATE);
        if (!prefs.getBoolean("logged_in", false)) {
            Toast.makeText(this, "Please login again", Toast.LENGTH_SHORT).show();
            startActivity(new Intent(this, LoginActivity.class));
            finish();
            return;
        }

        String fullName = prefs.getString("full_name", "Unknown");
        String email = prefs.getString("email", "Unknown");
        String phone = prefs.getString("phone", "Unknown");
        String role = prefs.getString("role", "shipper");

        tvName.setText("Name: " + fullName);
        tvEmail.setText("Email: " + email);
        tvPhone.setText("Phone: " + phone);
    }

    private void logout() {
        SharedPreferences.Editor editor = getSharedPreferences("user_data", MODE_PRIVATE).edit();
        editor.clear();
        editor.apply();

        Toast.makeText(this, "Logged out successfully", Toast.LENGTH_SHORT).show();
        startActivity(new Intent(this, LoginActivity.class));
        finish();
    }
}
