package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.CompletedOrdersAdapter;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

public class ShipperCompletedOrdersActivity extends BaseActivity {
    private RecyclerView recyclerView;
    private CompletedOrdersAdapter adapter;
    private ProgressBar progressBar;

    private ArrayList<JSONObject> orders = new ArrayList<>();
    private ApiService apiService = new ApiService();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_shipper_completed_orders);

        recyclerView = findViewById(R.id.recycler_completed_orders);
        progressBar = findViewById(R.id.progress_bar);

        adapter = new CompletedOrdersAdapter(this, orders);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
        loadCompletedOrders();
        setupBottomNav();
    }

    private void loadCompletedOrders() {
        progressBar.setVisibility(View.VISIBLE);
        apiService.getCompletedOrders(new ApiService.OrdersCallback() {
            @Override
            public void onSuccess(JSONArray jsonOrders) {
                progressBar.setVisibility(View.GONE);
                orders.clear();
                for (int i = 0; i < jsonOrders.length(); i++) {
                    try {
                        orders.add(jsonOrders.getJSONObject(i));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                adapter.notifyDataSetChanged();
            }

            @Override
            public void onError(String error) {
                progressBar.setVisibility(View.GONE);
                Toast.makeText(ShipperCompletedOrdersActivity.this, error, Toast.LENGTH_SHORT).show();
            }
        });
    }
}
