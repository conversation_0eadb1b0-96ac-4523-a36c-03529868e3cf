<<<<<<< HEAD
package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ShipperEarningsSummaryActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_earnings_summary);

        TextView tvTotal = findViewById(R.id.tv_total_earnings);
        TextView tvBreakdown = findViewById(R.id.tv_earnings_breakdown);

        // Dữ liệu giả
        int delivery1 = 35000;
        int delivery2 = 42000;
        int delivery3 = 50000;

        int total = delivery1 + delivery2 + delivery3;

        String breakdown = "Delivery #ORD101: 35,000 VND\n"
                + "Delivery #ORD102: 42,000 VND\n"
                + "Delivery #ORD103: 50,000 VND";

        tvTotal.setText("Total Earnings: " + total + " VND");
        tvBreakdown.setText(breakdown);
    }
}
=======
package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONObject;

public class ShipperEarningsSummaryActivity extends BaseActivity {

    private TextView tvCompletedOrders, tvTotalEarnings, tvAveragePerOrder;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_earnings_summary);
        Button btnViewCompletedOrders = findViewById(R.id.btn_view_completed_orders);
        btnViewCompletedOrders.setOnClickListener(v -> {
            Intent intent = new Intent(ShipperEarningsSummaryActivity.this, ShipperCompletedOrdersActivity.class);
            startActivity(intent);
        });
        tvCompletedOrders = findViewById(R.id.tv_completed_orders);
        tvTotalEarnings = findViewById(R.id.tv_total_earnings);
        tvAveragePerOrder = findViewById(R.id.tv_average_per_order);
        setupBottomNav();
        setSelectedNavItem(R.id.nav_earnings);
        loadEarnings();
    }
    @Override
    protected void onResume() {
        super.onResume();
        setSelectedNavItem(R.id.nav_earnings);}
    private void loadEarnings() {
        ApiService apiService = new ApiService();

        apiService.getShipperEarnings(new ApiService.EarningsCallback() {
            @Override
            public void onSuccess(JSONObject data) {
                int completedOrders = data.optInt("completed_orders", 0);
                double totalEarnings = data.optDouble("total_earnings", 0.0);
                double averagePerOrder = data.optDouble("average_per_order", 0.0);

                tvCompletedOrders.setText("Completed Orders: " + completedOrders);
                tvTotalEarnings.setText(String.format("Total Earnings: $%.2f", totalEarnings));
                tvAveragePerOrder.setText(String.format("Average per Order: $%.2f", averagePerOrder));
            }

            @Override
            public void onError(String error) {
                Toast.makeText(ShipperEarningsSummaryActivity.this, error, Toast.LENGTH_SHORT).show();
            }
        });
    }
}
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
