package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;

public class ManagerDashboardActivity extends AppCompatActivity {

    private TextView tvWelcome;
    private Button btnLogout;
    private Button btnAccountManagement;
<<<<<<< HEAD
=======
    private Button btnPromotionManagement;
    private Button btnMenuItemManagement;

>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_manager_dashboard);

        initViews();
        setupClickListeners();
    }

    private void initViews() {
        tvWelcome = findViewById(R.id.tv_welcome);
        btnLogout = findViewById(R.id.btn_logout);
        btnAccountManagement = findViewById(R.id.btn_account_management);
<<<<<<< HEAD

=======
        btnPromotionManagement = findViewById(R.id.btn_promotion_management);
        btnMenuItemManagement = findViewById(R.id.btn_menu_item_management);
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
        tvWelcome.setText("Welcome to Manager Dashboard!\n\nYou are logged in as: MANAGER");
    }

    private void setupClickListeners() {
        btnLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logout();
            }
        });

        btnAccountManagement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openAccountManagement();
            }
        });
<<<<<<< HEAD
=======

        btnPromotionManagement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openPromotionManagement();
            }
        });

        btnMenuItemManagement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openMenuItemManagement();
            }
        });
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
    }

    private void logout() {
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private void openAccountManagement() {
        Intent intent = new Intent(this, AccountManagementActivity.class);
        startActivity(intent);
    }
<<<<<<< HEAD
=======

    private void openPromotionManagement() {
        Intent intent = new Intent(this, PromotionManagementActivity.class);
        startActivity(intent);
    }

    private void openMenuItemManagement() {
        Intent intent = new Intent(this, MenuItemManagementActivity.class);
        startActivity(intent);
    }
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
}
