package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.google.android.material.bottomnavigation.BottomNavigationView;

public abstract class BaseActivity extends AppCompatActivity {

    protected BottomNavigationView bottomNavigation;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    protected void setupBottomNav() {
        bottomNavigation = findViewById(R.id.bottom_navigation);
        if (bottomNavigation == null) return;

        bottomNavigation.setOnItemSelectedListener(item -> {
            int id = item.getItemId();

            if (id == R.id.nav_home && !(this instanceof ShipperDashboardActivity)) {
                Intent intent = new Intent(this, ShipperDashboardActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                startActivity(intent);

            } else if (id == R.id.nav_assigned_orders && !(this instanceof ShipperViewAssignedOrdersActivity)) {
                Intent intent = new Intent(this, ShipperViewAssignedOrdersActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                startActivity(intent);

            } else if (id == R.id.nav_earnings && !(this instanceof ShipperEarningsSummaryActivity)) {
                Intent intent = new Intent(this, ShipperEarningsSummaryActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                startActivity(intent);
            } else if (id == R.id.nav_profile && !(this instanceof ShipperProfile)) {
                Intent intent = new Intent(this, ShipperProfile.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                startActivity(intent);
            }

            return true;
        });
    }

    protected void setSelectedNavItem(int itemId) {
        if (bottomNavigation != null) {
            bottomNavigation.setSelectedItemId(itemId);
        }
    }
}
