package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.ReviewAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.CartItem;
import com.example.coffeeshop.models.Product;
import com.example.coffeeshop.models.Review;
import com.google.android.material.button.MaterialButton;

import java.math.BigDecimal;
import java.util.List;

public class ProductDetailsActivity extends AppCompatActivity {

    private ImageView ivProductImage, ivBack;
    private TextView tvProductName, tvProductPrice, tvProductDescription, tvRating, tvReviewCount;
    private MaterialButton btnAddToCart;
    private RecyclerView rvReviews;
    
    private MockDataManager dataManager;
    private Product product;
    private ReviewAdapter reviewAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_product_details);

        dataManager = MockDataManager.getInstance();
        
        // Get product ID from intent
        int productId = getIntent().getIntExtra("PRODUCT_ID", -1);
        if (productId == -1) {
            Toast.makeText(this, "Product not found", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        product = dataManager.getProductById(productId);
        if (product == null) {
            Toast.makeText(this, "Product not found", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        initViews();
        setupProduct();
        setupReviews();
    }

    private void initViews() {
        ivProductImage = findViewById(R.id.iv_product_image);
        ivBack = findViewById(R.id.iv_back);
        tvProductName = findViewById(R.id.tv_product_name);
        tvProductPrice = findViewById(R.id.tv_product_price);
        tvProductDescription = findViewById(R.id.tv_product_description);
        tvRating = findViewById(R.id.tv_rating);
        tvReviewCount = findViewById(R.id.tv_review_count);
        btnAddToCart = findViewById(R.id.btn_add_to_cart);
        rvReviews = findViewById(R.id.rv_reviews);

        ivBack.setOnClickListener(v -> finish());
        btnAddToCart.setOnClickListener(v -> addToCart());
    }

    private void setupProduct() {
        tvProductName.setText(product.getName());
        tvProductPrice.setText("$" + product.getPrice().toString());
        tvProductDescription.setText(product.getDescription());
        
        // Set product image (using placeholder)
        // ivProductImage.setImageResource(R.drawable.placeholder_product);
        
        // Set rating and review count (fake data)
        double rating = 4.2 + (Math.random() * 0.8); // Random rating between 4.2-5.0
        int reviewCount = 15 + (int)(Math.random() * 85); // Random count 15-100
        
        tvRating.setText(String.format("%.1f", rating));
        tvReviewCount.setText("(" + reviewCount + " reviews)");
    }

    private void setupReviews() {
        List<Review> reviews = dataManager.getProductReviews(product.getProductId());
        reviewAdapter = new ReviewAdapter(this, reviews);
        rvReviews.setLayoutManager(new LinearLayoutManager(this));
        rvReviews.setAdapter(reviewAdapter);
    }

    private void addToCart() {
        boolean success = dataManager.addToCart(1, product.getProductId(), 1); // userId=1, quantity=1
        if (success) {
            Toast.makeText(this, product.getName() + " added to cart!", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(this, "Failed to add to cart", Toast.LENGTH_SHORT).show();
        }
    }
}
