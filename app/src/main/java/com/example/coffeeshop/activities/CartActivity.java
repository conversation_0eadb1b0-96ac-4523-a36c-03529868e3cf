package com.example.coffeeshop.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.adapters.CartAdapter;
import com.example.coffeeshop.data.MockDataManager;
import com.example.coffeeshop.models.CartItem;

import java.math.BigDecimal;
import java.util.List;

public class CartActivity extends AppCompatActivity implements CartAdapter.OnCartItemActionListener {

    private Toolbar toolbar;
    private RecyclerView rvCartItems;
    private TextView tvEmptyCart, tvSubtotal, tvTotal;
    private Button btnCheckout, btnClearCart;
    
    private MockDataManager dataManager;
    private CartAdapter cartAdapter;
    private int userId;    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_cart_new);

        userId = getIntent().getIntExtra("user_id", 1);
        
        initViews();
        dataManager = MockDataManager.getInstance();
        setupToolbar();
        setupCartList();
        updateCartSummary();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        rvCartItems = findViewById(R.id.rv_cart_items);
        tvEmptyCart = findViewById(R.id.tv_empty_cart);
        tvSubtotal = findViewById(R.id.tv_subtotal);
        tvTotal = findViewById(R.id.tv_total);
        btnCheckout = findViewById(R.id.btn_checkout);
        btnClearCart = findViewById(R.id.btn_clear_cart);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Your Cart");
        }
        
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    private void setupCartList() {
        List<CartItem> cartItems = dataManager.getCartItems(userId);
        cartAdapter = new CartAdapter(this, cartItems);
        cartAdapter.setOnCartItemActionListener(this);
        
        rvCartItems.setLayoutManager(new LinearLayoutManager(this));
        rvCartItems.setAdapter(cartAdapter);

        updateCartVisibility(cartItems);
        setupClickListeners();
    }    private void setupClickListeners() {
        btnCheckout.setOnClickListener(v -> {
            List<CartItem> cartItems = dataManager.getCartItems(userId);
            if (cartItems.isEmpty()) {
                Toast.makeText(this, "Your cart is empty!", Toast.LENGTH_SHORT).show();
                return;
            }
            proceedToCheckout();
        });

        btnClearCart.setOnClickListener(v -> {
            dataManager.clearCart(userId);
            Toast.makeText(this, "Cart cleared", Toast.LENGTH_SHORT).show();
            refreshCart();
        });
    }

    private void proceedToCheckout() {
        BigDecimal subtotal = dataManager.getCartTotal(userId);
        BigDecimal tax = subtotal.multiply(new BigDecimal("0.08"));
        BigDecimal shipping = subtotal.compareTo(new BigDecimal("25.00")) >= 0 ? 
                             BigDecimal.ZERO : new BigDecimal("3.99");
        BigDecimal total = subtotal.add(tax).add(shipping);
        
        String orderSummary = String.format(
            "Order Summary:\nSubtotal: $%.2f\nTax: $%.2f\nShipping: $%.2f\nTotal: $%.2f\n\nThis is a demo checkout.",
            subtotal, tax, shipping, total
        );
        
        new androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Checkout")
            .setMessage(orderSummary)
            .setPositiveButton("Confirm Order", (dialog, which) -> {
                // In a real app, this would process the order
                dataManager.clearCart(userId);
                Toast.makeText(this, "Order placed successfully! (Demo)", Toast.LENGTH_LONG).show();
                finish(); // Return to dashboard
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void updateCartVisibility(List<CartItem> cartItems) {
        if (cartItems.isEmpty()) {
            rvCartItems.setVisibility(View.GONE);
            tvEmptyCart.setVisibility(View.VISIBLE);
            btnCheckout.setEnabled(false);
            btnClearCart.setEnabled(false);
        } else {
            rvCartItems.setVisibility(View.VISIBLE);
            tvEmptyCart.setVisibility(View.GONE);
            btnCheckout.setEnabled(true);
            btnClearCart.setEnabled(true);
        }
    }    private void updateCartSummary() {
        BigDecimal subtotal = dataManager.getCartTotal(userId);
        BigDecimal tax = subtotal.multiply(new BigDecimal("0.08")); // 8% tax
        BigDecimal shipping = subtotal.compareTo(new BigDecimal("25.00")) >= 0 ? 
                             BigDecimal.ZERO : new BigDecimal("3.99"); // Free shipping over $25
        BigDecimal total = subtotal.add(tax).add(shipping);
        
        tvSubtotal.setText(String.format("Subtotal: $%.2f", subtotal));
        
        // Update total with tax and shipping details
        String totalText = String.format("Total: $%.2f", total);
        if (tax.compareTo(BigDecimal.ZERO) > 0) {
            totalText += String.format("\n(+$%.2f tax)", tax);
        }
        if (shipping.compareTo(BigDecimal.ZERO) > 0) {
            totalText += String.format("\n(+$%.2f shipping)", shipping);
        } else if (subtotal.compareTo(new BigDecimal("25.00")) >= 0) {
            totalText += "\n(FREE shipping!)";
        }
        tvTotal.setText(totalText);
    }

    private void refreshCart() {
        List<CartItem> updatedCartItems = dataManager.getCartItems(userId);
        cartAdapter.updateCartItems(updatedCartItems);
        updateCartVisibility(updatedCartItems);
        updateCartSummary();
    }

    @Override
    public void onQuantityChanged(CartItem cartItem, int newQuantity) {
        boolean success = dataManager.updateCartItemQuantity(cartItem.getCartItemId(), newQuantity);
        if (success) {
            refreshCart();
        } else {
            Toast.makeText(this, "Unable to update quantity. Check stock availability.", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onRemoveItem(CartItem cartItem) {
        boolean success = dataManager.removeFromCart(cartItem.getCartItemId());
        if (success) {
            Toast.makeText(this, cartItem.getProduct().getName() + " removed from cart", Toast.LENGTH_SHORT).show();
            refreshCart();
        }
    }
}
