package com.example.coffeeshop.activities;

import android.app.DatePickerDialog;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class EditPromotionActivity extends AppCompatActivity {

    private static final String TAG = "EditPromotionActivity";

    private Toolbar toolbar;
    private TextInputEditText etPromotionCode, etPromotionName, etPromotionType, etPromotionValue;
    private TextInputEditText etBuyQuantity, etGetQuantity, etStartDate, etEndDate, etUsageLimit, etUsageCount;
    private AutoCompleteTextView spinnerApplicableProducts, spinnerStatus;
    private TextInputLayout layoutPromotionValue;
    private LinearLayout layoutBuyXGetY; // Changed to LinearLayout
    private MaterialButton btnCancel, btnSave;

    private Calendar startCalendar = Calendar.getInstance();
    private Calendar endCalendar = Calendar.getInstance();
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    
    private ApiService apiService;
    private int promotionId = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "Starting onCreate");
            setContentView(R.layout.activity_edit_promotion);
            Log.d(TAG, "Layout inflated successfully");

            initViews();
            Log.d(TAG, "Views initialized");

            setupToolbar();
            setupSpinners();
            setupDatePickers();
            setupClickListeners();
            
            // Initialize API service
            apiService = new ApiService();
            
            // Get promotion ID from intent
            promotionId = getIntent().getIntExtra("promotion_id", -1);
            
            if (promotionId != -1) {
                loadPromotionData();
            } else {
                // Load from old intent format for backward compatibility
                loadPromotionDataFromIntent();
            }

            Log.d(TAG, "Activity setup completed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            e.printStackTrace();

            // Show error message and finish
            Toast.makeText(this, "Error loading Edit Promotion screen: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etPromotionCode = findViewById(R.id.et_promotion_code);
        etPromotionName = findViewById(R.id.et_promotion_name);
        etPromotionType = findViewById(R.id.et_promotion_type);
        etPromotionValue = findViewById(R.id.et_promotion_value);
        etBuyQuantity = findViewById(R.id.et_buy_quantity);
        etGetQuantity = findViewById(R.id.et_get_quantity);
        etStartDate = findViewById(R.id.et_start_date);
        etEndDate = findViewById(R.id.et_end_date);
        etUsageLimit = findViewById(R.id.et_usage_limit);
        etUsageCount = findViewById(R.id.et_usage_count);

        spinnerApplicableProducts = findViewById(R.id.spinner_applicable_products);
        spinnerStatus = findViewById(R.id.spinner_status);

        layoutPromotionValue = findViewById(R.id.layout_promotion_value);
        layoutBuyXGetY = findViewById(R.id.layout_buy_x_get_y); // Now correctly cast to LinearLayout

        btnCancel = findViewById(R.id.btn_cancel);
        btnSave = findViewById(R.id.btn_save);

        // Log any null views for debugging
        if (layoutBuyXGetY == null) {
            Log.w(TAG, "layoutBuyXGetY is null - check if layout_buy_x_get_y exists in XML");
        }
        if (layoutPromotionValue == null) {
            Log.w(TAG, "layoutPromotionValue is null - check if layout_promotion_value exists in XML");
        }
    }

    private void setupToolbar() {
        if (toolbar != null) {
            setSupportActionBar(toolbar);
            if (getSupportActionBar() != null) {
                getSupportActionBar().setDisplayHomeAsUpEnabled(true);
                getSupportActionBar().setTitle("Edit Promotion");
            }
            toolbar.setNavigationOnClickListener(v -> onBackPressed());
        }
    }

    private void setupSpinners() {
        // Applicable Products
        String[] products = {"All Products", "Coffee", "Tea", "Pastries", "Hot Drinks", "Cold Drinks", "Sandwiches"};
        ArrayAdapter<String> productsAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, products);
        if (spinnerApplicableProducts != null) {
            spinnerApplicableProducts.setAdapter(productsAdapter);
        }

        // Status (don't include Expired as it's auto-managed)
        String[] statuses = {"Active", "Inactive"};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, statuses);
        if (spinnerStatus != null) {
            spinnerStatus.setAdapter(statusAdapter);
        }
    }

    private void setupDatePickers() {
        if (etStartDate != null) {
            etStartDate.setOnClickListener(v -> showDatePicker(true));
        }
        if (etEndDate != null) {
            etEndDate.setOnClickListener(v -> showDatePicker(false));
        }
    }

    private void showDatePicker(boolean isStartDate) {
        Calendar calendar = isStartDate ? startCalendar : endCalendar;

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    String formattedDate = dateFormat.format(calendar.getTime());

                    if (isStartDate && etStartDate != null) {
                        etStartDate.setText(formattedDate);
                    } else if (!isStartDate && etEndDate != null) {
                        etEndDate.setText(formattedDate);
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        if (!isStartDate) {
            datePickerDialog.getDatePicker().setMinDate(startCalendar.getTimeInMillis());
        }

        datePickerDialog.show();
    }

    private void setupClickListeners() {
        if (btnCancel != null) {
            btnCancel.setOnClickListener(v -> onBackPressed());
        }
        if (btnSave != null) {
            btnSave.setOnClickListener(v -> updatePromotion());
        }
    }

    private void loadPromotionData() {
        if (promotionId == -1) {
            Toast.makeText(this, "Invalid promotion ID", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // Call API to get promotion details
        apiService.getPromotion(promotionId, new ApiService.PromotionCallback() {
            @Override
            public void onSuccess(JSONObject promotionData) {
                try {
                    // Populate fields from API response
                    String title = promotionData.optString("title", "");
                    String description = promotionData.optString("description", "");
                    String promoCode = promotionData.optString("promo_code", "");
                    String discountType = promotionData.optString("discount_type", "");
                    double discountValue = promotionData.optDouble("discount_value", 0.0);
                    double minimumOrderAmount = promotionData.optDouble("minimum_order_amount", 0.0);
                    String startDate = promotionData.optString("start_date", "");
                    String endDate = promotionData.optString("end_date", "");
                    boolean isActive = promotionData.optInt("is_active", 0) == 1;
                    Integer usageLimit = promotionData.isNull("usage_limit") ? null : promotionData.optInt("usage_limit", 0);
                    int currentUsage = promotionData.optInt("current_usage", 0);
                    String applicableProducts = promotionData.optString("applicable_products", "");

                    // Populate UI fields
                    if (etPromotionCode != null) etPromotionCode.setText(promoCode);
                    if (etPromotionName != null) etPromotionName.setText(title);
                    if (etStartDate != null) etStartDate.setText(startDate);
                    if (etEndDate != null) etEndDate.setText(endDate);
                    if (etUsageLimit != null && usageLimit != null) etUsageLimit.setText(String.valueOf(usageLimit));
                    if (etUsageCount != null) etUsageCount.setText(String.valueOf(currentUsage));

                    if (spinnerApplicableProducts != null) {
                        spinnerApplicableProducts.setText(applicableProducts.isEmpty() ? "All Products" : applicableProducts, false);
                    }
                    if (spinnerStatus != null) {
                        spinnerStatus.setText(isActive ? "Active" : "Inactive", false);
                    }

                    // Convert API discount type to display type
                    String displayType = convertDiscountTypeToDisplay(discountType);
                    if (etPromotionType != null) {
                        etPromotionType.setText(displayType);
                    }

                    // Handle type-specific fields
                    handlePromotionTypeDisplay(displayType, String.valueOf(discountValue));

                    // Parse dates for calendars
                    try {
                        if (!startDate.isEmpty()) {
                            startCalendar.setTime(dateFormat.parse(startDate));
                        }
                        if (!endDate.isEmpty()) {
                            endCalendar.setTime(dateFormat.parse(endDate));
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error parsing dates", e);
                    }

                    Log.d(TAG, "Promotion data loaded successfully for: " + promoCode);

                } catch (Exception e) {
                    Log.e(TAG, "Error processing promotion data", e);
                    Toast.makeText(EditPromotionActivity.this, "Error loading promotion data", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onError(String error) {
                Toast.makeText(EditPromotionActivity.this, "Error loading promotion: " + error, Toast.LENGTH_SHORT).show();
                finish();
            }
        });
    }

    private void loadPromotionDataFromIntent() {
        try {
            // Fallback method for old intent format
            String title = getIntent().getStringExtra("title");
            String description = getIntent().getStringExtra("description");
            String discountType = getIntent().getStringExtra("discount_type");
            double discountValue = getIntent().getDoubleExtra("discount_value", 0.0);
            double minimumOrderAmount = getIntent().getDoubleExtra("minimum_order_amount", 0.0);
            String startDate = getIntent().getStringExtra("start_date");
            String endDate = getIntent().getStringExtra("end_date");
            boolean isActive = getIntent().getBooleanExtra("is_active", true);
            Integer usageLimit = getIntent().hasExtra("usage_limit") ? getIntent().getIntExtra("usage_limit", 0) : null;
            String applicableProducts = getIntent().getStringExtra("applicable_products");
            String promoCode = getIntent().getStringExtra("promo_code");

            // Populate fields with null checks
            if (etPromotionCode != null) etPromotionCode.setText(promoCode != null ? promoCode : "");
            if (etPromotionName != null) etPromotionName.setText(title != null ? title : "");
            if (etStartDate != null) etStartDate.setText(startDate != null ? startDate : "");
            if (etEndDate != null) etEndDate.setText(endDate != null ? endDate : "");
            if (etUsageLimit != null && usageLimit != null) etUsageLimit.setText(String.valueOf(usageLimit));

            if (spinnerApplicableProducts != null) {
                spinnerApplicableProducts.setText(applicableProducts != null ? applicableProducts : "All Products", false);
            }
            if (spinnerStatus != null) {
                spinnerStatus.setText(isActive ? "Active" : "Inactive", false);
            }

            // Convert API discount type to display type
            String displayType = convertDiscountTypeToDisplay(discountType);
            if (etPromotionType != null) {
                etPromotionType.setText(displayType);
            }

            // Handle type-specific fields
            handlePromotionTypeDisplay(displayType, String.valueOf(discountValue));

            // Parse dates for calendars
            try {
                if (startDate != null && !startDate.isEmpty()) {
                    startCalendar.setTime(dateFormat.parse(startDate));
                }
                if (endDate != null && !endDate.isEmpty()) {
                    endCalendar.setTime(dateFormat.parse(endDate));
                }
            } catch (Exception e) {
                Log.e(TAG, "Error parsing dates", e);
            }

            Log.d(TAG, "Promotion data loaded from intent successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error loading promotion data from intent", e);
            Toast.makeText(this, "Error loading promotion data", Toast.LENGTH_SHORT).show();
        }
    }

    private String convertDiscountTypeToDisplay(String apiType) {
        if (apiType == null) return "Percentage";
        
        switch (apiType) {
            case "percentage":
                return "Percentage";
            case "fixed_amount":
                return "Fixed Amount";
            case "free_shipping":
                return "Buy X Get Y";
            default:
                return "Percentage";
        }
    }

    private void handlePromotionTypeDisplay(String type, String value) {
        try {
            switch (type) {
                case "Percentage":
                    if (layoutPromotionValue != null) {
                        layoutPromotionValue.setHint("Percentage (e.g., 20)");
                        layoutPromotionValue.setVisibility(View.VISIBLE);
                    }
                    if (layoutBuyXGetY != null) {
                        layoutBuyXGetY.setVisibility(View.GONE);
                    }
                    // Remove % symbol for editing
                    String percentValue = value.replace("%", "");
                    if (etPromotionValue != null) {
                        etPromotionValue.setText(percentValue);
                    }
                    break;
                case "Fixed Amount":
                    if (layoutPromotionValue != null) {
                        layoutPromotionValue.setHint("Fixed Amount (e.g., 5.00)");
                        layoutPromotionValue.setVisibility(View.VISIBLE);
                    }
                    if (layoutBuyXGetY != null) {
                        layoutBuyXGetY.setVisibility(View.GONE);
                    }
                    // Remove $ symbol for editing
                    String fixedValue = value.replace("$", "");
                    if (etPromotionValue != null) {
                        etPromotionValue.setText(fixedValue);
                    }
                    break;
                case "Buy X Get Y":
                    if (layoutPromotionValue != null) {
                        layoutPromotionValue.setVisibility(View.GONE);
                    }
                    if (layoutBuyXGetY != null) {
                        layoutBuyXGetY.setVisibility(View.VISIBLE);
                    }
                    // Parse "Buy 2 Get 1" format
                    String[] parts = value.split(" ");
                    if (parts.length >= 4) {
                        if (etBuyQuantity != null) {
                            etBuyQuantity.setText(parts[1]);
                        }
                        if (etGetQuantity != null) {
                            etGetQuantity.setText(parts[3]);
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling promotion type display", e);
        }
    }

    private void updatePromotion() {
        try {
            if (!validateInputs()) {
                return;
            }

            if (promotionId == -1) {
                Toast.makeText(this, "Invalid promotion ID", Toast.LENGTH_SHORT).show();
                return;
            }

            // Disable button to prevent double submission
            btnSave.setEnabled(false);
            btnSave.setText("Updating...");

            // Get form data
            String title = etPromotionName != null ? etPromotionName.getText().toString().trim() : "";
            String description = "Updated promotion: " + title; // Simple description
            String promoCode = etPromotionCode != null ? etPromotionCode.getText().toString().trim().toUpperCase() : null;
            String displayType = etPromotionType != null ? etPromotionType.getText().toString() : "";
            String startDate = etStartDate != null ? etStartDate.getText().toString() : "";
            String endDate = etEndDate != null ? etEndDate.getText().toString() : "";
            String status = spinnerStatus != null ? spinnerStatus.getText().toString() : "";
            String applicableProducts = spinnerApplicableProducts != null ? spinnerApplicableProducts.getText().toString() : null;
            
            // Convert display type and value to API format
            String discountType;
            double discountValue = 0.0;
            
            switch (displayType) {
                case "Percentage":
                    discountType = "percentage";
                    try {
                        String percentValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "0";
                        discountValue = Double.parseDouble(percentValue);
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Error parsing percentage value", e);
                    }
                    break;
                case "Fixed Amount":
                    discountType = "fixed_amount";
                    try {
                        String fixedValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "0";
                        discountValue = Double.parseDouble(fixedValue);
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Error parsing fixed amount value", e);
                    }
                    break;
                case "Buy X Get Y":
                    discountType = "free_shipping"; // Using free_shipping as closest equivalent
                    discountValue = 0.0; // No monetary value for Buy X Get Y
                    break;
                default:
                    discountType = "percentage";
                    break;
            }
            
            // Get usage limit
            Integer usageLimit = null;
            try {
                String usageLimitText = etUsageLimit != null ? etUsageLimit.getText().toString() : "";
                if (!usageLimitText.isEmpty()) {
                    usageLimit = Integer.parseInt(usageLimitText);
                }
            } catch (NumberFormatException e) {
                Log.e(TAG, "Error parsing usage limit", e);
            }
            
            // Convert status to boolean
            boolean isActive = "Active".equals(status);
            
            // Set minimum order amount (could be enhanced with UI field)
            double minimumOrderAmount = 0.0;

            // Call API to update promotion
            apiService.updatePromotion(promotionId, title, description, discountType, discountValue, 
                    minimumOrderAmount, startDate, endDate, isActive, usageLimit, 
                    applicableProducts, promoCode, new ApiService.PromotionActionCallback() {
                @Override
                public void onSuccess(JSONObject result) {
                    // Reset button state
                    btnSave.setEnabled(true);
                    btnSave.setText("Save");
                    
                    Toast.makeText(EditPromotionActivity.this, 
                            "Promotion '" + title + "' updated successfully!", 
                            Toast.LENGTH_SHORT).show();
                    finish();
                }

                @Override
                public void onError(String error) {
                    // Reset button state
                    btnSave.setEnabled(true);
                    btnSave.setText("Save");
                    
                    Toast.makeText(EditPromotionActivity.this, 
                            "Error updating promotion: " + error, 
                            Toast.LENGTH_LONG).show();
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error updating promotion", e);
            
            // Reset button state
            btnSave.setEnabled(true);
            btnSave.setText("Save");
            
            Toast.makeText(this, "Error updating promotion", Toast.LENGTH_SHORT).show();
        }
    }

    private String getPromotionValue() {
        try {
            String type = etPromotionType != null ? etPromotionType.getText().toString() : "";

            switch (type) {
                case "Percentage":
                    String percentValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "";
                    return percentValue + "%";
                case "Fixed Amount":
                    String fixedValue = etPromotionValue != null ? etPromotionValue.getText().toString() : "";
                    return "$" + fixedValue;
                case "Buy X Get Y":
                    String buyQty = etBuyQuantity != null ? etBuyQuantity.getText().toString() : "";
                    String getQty = etGetQuantity != null ? etGetQuantity.getText().toString() : "";
                    return "Buy " + buyQty + " Get " + getQty;
                default:
                    return etPromotionValue != null ? etPromotionValue.getText().toString() : "";
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting promotion value", e);
            return "";
        }
    }

    private boolean validateInputs() {
        try {
            // Similar validation as AddPromotionActivity but without code validation
            if (etPromotionName == null || etPromotionName.getText().toString().trim().isEmpty()) {
                if (etPromotionName != null) {
                    etPromotionName.setError("Promotion name is required");
                    etPromotionName.requestFocus();
                }
                return false;
            }

            // Type-specific validation
            String type = etPromotionType != null ? etPromotionType.getText().toString() : "";
            switch (type) {
                case "Percentage":
                case "Fixed Amount":
                    if (etPromotionValue == null || etPromotionValue.getText().toString().trim().isEmpty()) {
                        if (etPromotionValue != null) {
                            etPromotionValue.setError("Promotion value is required");
                            etPromotionValue.requestFocus();
                        }
                        return false;
                    }
                    break;
                case "Buy X Get Y":
                    if (etBuyQuantity == null || etBuyQuantity.getText().toString().trim().isEmpty()) {
                        if (etBuyQuantity != null) {
                            etBuyQuantity.setError("Buy quantity is required");
                            etBuyQuantity.requestFocus();
                        }
                        return false;
                    }
                    if (etGetQuantity == null || etGetQuantity.getText().toString().trim().isEmpty()) {
                        if (etGetQuantity != null) {
                            etGetQuantity.setError("Get quantity is required");
                            etGetQuantity.requestFocus();
                        }
                        return false;
                    }
                    break;
            }

            // Date validation
            if (etStartDate == null || etStartDate.getText().toString().isEmpty()) {
                Toast.makeText(this, "Start date is required", Toast.LENGTH_SHORT).show();
                return false;
            }

            if (etEndDate == null || etEndDate.getText().toString().isEmpty()) {
                Toast.makeText(this, "End date is required", Toast.LENGTH_SHORT).show();
                return false;
            }

            if (endCalendar.before(startCalendar)) {
                Toast.makeText(this, "End date must be after start date", Toast.LENGTH_SHORT).show();
                return false;
            }

            return true;

        } catch (Exception e) {
            Log.e(TAG, "Error validating inputs", e);
            Toast.makeText(this, "Validation error", Toast.LENGTH_SHORT).show();
            return false;
        }
    }
}