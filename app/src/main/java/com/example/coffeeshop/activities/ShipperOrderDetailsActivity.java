<<<<<<< HEAD
package com.example.coffeeshop.activities;

import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.coffeeshop.R;
import com.google.android.material.button.MaterialButton;

public class ShipperOrderDetailsActivity extends AppCompatActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_order_details);

        String orderId = getIntent().getStringExtra("orderId");
        String customer = getIntent().getStringExtra("customer");
        String address = getIntent().getStringExtra("address");
        String status = getIntent().getStringExtra("status");

        String info = "Order ID: " + orderId + "\nCustomer: " + customer + "\nAddress: " + address + "\nStatus: ";

        SpannableString spannable = new SpannableString(info + status);
        int start = info.length();
        int end = start + status.length();

        int color;
        if ("Accepted".equalsIgnoreCase(status)) {
            color = Color.parseColor("#008000");
        } else if ("Rejected".equalsIgnoreCase(status)) {
            color = Color.parseColor("#8B0000");
        } else {
            color = Color.BLACK;
        }

        spannable.setSpan(new ForegroundColorSpan(color), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        TextView tvInfo = findViewById(R.id.tv_order_info);
        tvInfo.setText(spannable);

        // Xử lý nút chuyển sang Update Status
        MaterialButton btnUpdate = findViewById(R.id.btn_update_status);
        btnUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(ShipperOrderDetailsActivity.this, ShipperUpdateOrderStatusActivity.class);
                intent.putExtra("orderId", orderId); // truyền thêm nếu cần
                startActivity(intent);
            }
        });

        MaterialButton btnContact = findViewById(R.id.btn_contact_customer);
        btnContact.setOnClickListener(v -> {
            Intent intent = new Intent(this, ShipperContactCustomerActivity.class);
            startActivity(intent);
        });

        MaterialButton btnViewMap = findViewById(R.id.btn_view_map);
        btnViewMap.setOnClickListener(v -> {
            Intent intent = new Intent(ShipperOrderDetailsActivity.this, ShipperViewMapActivity.class);
            startActivity(intent);
        });


    }
}
=======
package com.example.coffeeshop.activities;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONObject;

public class ShipperOrderDetailsActivity extends BaseActivity {

    private int orderId;
    private TextView tvOrderId, tvCustomerName, tvAddress, tvTotalPrice, tvStatus, tvCreatedAt, tvInstructions;
    private Button btnMap, btnContact, btnUpdateStatus;

    private String customerPhone = "";
    private String deliveryAddress = "";

    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_shipper_order_details);
        setupBottomNav();
        orderId = getIntent().getIntExtra("order_id", -1);
        if (orderId == -1) {
            Toast.makeText(this, "Order ID invalid", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        apiService = new ApiService();

        tvOrderId = findViewById(R.id.tv_order_id);
        tvCustomerName = findViewById(R.id.tv_customer_name);
        tvAddress = findViewById(R.id.tv_delivery_address);
        tvTotalPrice = findViewById(R.id.tv_total_price);
        tvStatus = findViewById(R.id.tv_order_status);
        tvCreatedAt = findViewById(R.id.tv_created_at);
        tvInstructions = findViewById(R.id.tv_instructions);

        btnMap = findViewById(R.id.btn_view_map);
        btnContact = findViewById(R.id.btn_contact_customer);
        btnUpdateStatus = findViewById(R.id.btn_update_status);

        loadOrderDetail();

        btnMap.setOnClickListener(v -> openMap());
        btnContact.setOnClickListener(v -> contactCustomer());
        btnUpdateStatus.setOnClickListener(v -> updateOrderStatus());
    }

    private void loadOrderDetail() {
        apiService.getOrderDetail(orderId, new ApiService.OrderCallback() {
            @Override
            public void onSuccess(JSONObject order) {
                try {
                    tvOrderId.setText("Order #" + orderId);
                    tvCustomerName.setText(order.optString("customer_name"));
                    deliveryAddress = order.optString("delivery_address");
                    tvAddress.setText(deliveryAddress);
                    tvTotalPrice.setText("$" + order.optDouble("total_price", 0));
                    tvStatus.setText(order.optString("status"));
                    tvCreatedAt.setText(order.optString("created_at"));
                    tvInstructions.setText(order.optString("special_instructions", "None"));

                    customerPhone = order.optString("customer_phone", "");
                    Log.d("OrderDetail", "Customer phone: " + customerPhone);

                } catch (Exception e) {
                    Log.e("OrderDetail", "Parsing error", e);
                }
            }

            @Override
            public void onError(String error) {
                Toast.makeText(ShipperOrderDetailsActivity.this, "Error: " + error, Toast.LENGTH_SHORT).show();
                finish();
            }
        });
    }

    private void openMap() {
        if (!deliveryAddress.isEmpty()) {
            Uri gmmIntentUri = Uri.parse("geo:0,0?q=" + Uri.encode(deliveryAddress));
            Intent mapIntent = new Intent(Intent.ACTION_VIEW, gmmIntentUri);
            mapIntent.setPackage("com.google.android.apps.maps");
            startActivity(mapIntent);
        } else {
            Toast.makeText(this, "No address provided", Toast.LENGTH_SHORT).show();
        }
    }

    private void contactCustomer() {
        if (!customerPhone.isEmpty()) {
            Intent intent = new Intent(Intent.ACTION_DIAL);
            intent.setData(Uri.parse("tel:" + customerPhone));
            startActivity(intent);
        } else {
            Toast.makeText(this, "No phone number", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateOrderStatus() {
        apiService.updateOrderStatus(orderId, "completed", new ApiService.OrderActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                Toast.makeText(ShipperOrderDetailsActivity.this, "Order updated", Toast.LENGTH_SHORT).show();
                finish();
            }

            @Override
            public void onError(String error) {
                Toast.makeText(ShipperOrderDetailsActivity.this, "Error: " + error, Toast.LENGTH_SHORT).show();
            }
        });
    }
}
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
