package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONObject;

public class EditMenuItemActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private TextInputEditText etItemName, etDescription, etPrice, etImageUrl;
    private AutoCompleteTextView spinnerCategory, spinnerStatus;
    private MaterialButton btnCancel, btnSave;

    private ApiService apiService;
    private int menuItemId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_menu_item);

        initViews();
        setupToolbar();
        setupSpinners();
        setupClickListeners();
        loadMenuItemData();

        // Initialize API service
        apiService = new ApiService();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etItemName = findViewById(R.id.et_item_name);
        etDescription = findViewById(R.id.et_description);
        etPrice = findViewById(R.id.et_price);
        etImageUrl = findViewById(R.id.et_image_url);
        spinnerCategory = findViewById(R.id.spinner_category);
        spinnerStatus = findViewById(R.id.spinner_status);
        btnCancel = findViewById(R.id.btn_cancel);
        btnSave = findViewById(R.id.btn_save);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Edit Menu Item");
        }
        toolbar.setNavigationOnClickListener(v -> finish());
    }

    private void setupSpinners() {
        // Category spinner
        String[] categories = {"Hot Coffee", "Cold Coffee", "Tea", "Snacks", "Desserts"};
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this,
            android.R.layout.simple_dropdown_item_1line, categories);
        spinnerCategory.setAdapter(categoryAdapter);

        // Status spinner
        String[] statuses = {"active", "inactive"};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this,
            android.R.layout.simple_dropdown_item_1line, statuses);
        spinnerStatus.setAdapter(statusAdapter);
    }

    private void setupClickListeners() {
        btnCancel.setOnClickListener(v -> finish());
        btnSave.setOnClickListener(v -> updateMenuItem());
    }

    private void loadMenuItemData() {
        // Get data from intent
        menuItemId = getIntent().getIntExtra("menu_item_id", -1);
        String name = getIntent().getStringExtra("name");
        String description = getIntent().getStringExtra("description");
        double price = getIntent().getDoubleExtra("price", 0.0);
        String category = getIntent().getStringExtra("category");
        String imageUrl = getIntent().getStringExtra("image_url");
        String status = getIntent().getStringExtra("status");

        // Populate fields
        if (name != null) etItemName.setText(name);
        if (description != null) etDescription.setText(description);
        if (price > 0) etPrice.setText(String.valueOf(price));
        if (category != null) spinnerCategory.setText(category, false);
        if (imageUrl != null) etImageUrl.setText(imageUrl);
        if (status != null) spinnerStatus.setText(status, false);
    }

    private void updateMenuItem() {
        // Validate inputs
        String name = etItemName.getText() != null ? etItemName.getText().toString().trim() : "";
        String description = etDescription.getText() != null ? etDescription.getText().toString().trim() : "";
        String priceStr = etPrice.getText() != null ? etPrice.getText().toString().trim() : "";
        String category = spinnerCategory.getText().toString().trim();
        String status = spinnerStatus.getText().toString().trim();
        String imageUrl = etImageUrl.getText() != null ? etImageUrl.getText().toString().trim() : "";

        // Validation
        if (TextUtils.isEmpty(name)) {
            etItemName.setError("Item name is required");
            etItemName.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(priceStr)) {
            etPrice.setError("Price is required");
            etPrice.requestFocus();
            return;
        }

        double price;
        try {
            price = Double.parseDouble(priceStr);
            if (price <= 0) {
                etPrice.setError("Price must be greater than 0");
                etPrice.requestFocus();
                return;
            }
        } catch (NumberFormatException e) {
            etPrice.setError("Invalid price format");
            etPrice.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(category)) {
            Toast.makeText(this, "Please select a category", Toast.LENGTH_SHORT).show();
            return;
        }

        if (TextUtils.isEmpty(status)) {
            Toast.makeText(this, "Please select a status", Toast.LENGTH_SHORT).show();
            return;
        }

        // Show loading state
        btnSave.setEnabled(false);
        btnSave.setText("Updating...");

        // Update menu item using real API service
        apiService.updateMenuItem(menuItemId, name, description, price, category,
            imageUrl, true, status, new ApiService.MenuItemActionCallback() {
            @Override
            public void onSuccess(JSONObject result) {
                runOnUiThread(() -> {
                    Toast.makeText(EditMenuItemActivity.this,
                        "Menu item updated successfully!", Toast.LENGTH_SHORT).show();
                    finish();
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    btnSave.setEnabled(true);
                    btnSave.setText("Update Menu Item");
                    Toast.makeText(EditMenuItemActivity.this,
                        "Error: " + error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }
}
