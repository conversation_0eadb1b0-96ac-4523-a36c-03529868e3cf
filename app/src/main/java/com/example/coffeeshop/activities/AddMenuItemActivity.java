package com.example.coffeeshop.activities;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;

import com.example.coffeeshop.R;
import com.example.coffeeshop.api.ApiService;

import org.json.JSONObject;

public class AddMenuItemActivity extends AppCompatActivity {

    private Toolbar toolbar;
    private TextInputEditText etItemName, etDescription, etPrice, etImageUrl;
    private AutoCompleteTextView spinnerCategory, spinnerStatus;
    private MaterialButton btnCancel, btnSave;

    private ApiService apiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_menu_item);

        initViews();
        setupToolbar();
        setupSpinners();
        setupClickListeners();

        // Initialize API service
        apiService = new ApiService();
    }

    private void initViews() {
        toolbar = findViewById(R.id.toolbar);
        etItemName = findViewById(R.id.et_item_name);
        etDescription = findViewById(R.id.et_description);
        etPrice = findViewById(R.id.et_price);
        etImageUrl = findViewById(R.id.et_image_url);
        spinnerCategory = findViewById(R.id.spinner_category);
        spinnerStatus = findViewById(R.id.spinner_status);
        btnCancel = findViewById(R.id.btn_cancel);
        btnSave = findViewById(R.id.btn_save);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Add Menu Item");
        }
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void setupSpinners() {
        // Category options
        String[] categories = {"Hot Coffee", "Cold Coffee", "Tea", "Pastries", "Sandwiches", "Desserts", "Beverages"};
        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, categories);
        spinnerCategory.setAdapter(categoryAdapter);

        // Status options
        String[] statusOptions = {"Active", "Inactive", "Coming Soon"};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_dropdown_item_1line, statusOptions);
        spinnerStatus.setAdapter(statusAdapter);
        spinnerStatus.setText("Active", false); // Set default value
    }

    private void setupClickListeners() {
        btnCancel.setOnClickListener(v -> onBackPressed());
        btnSave.setOnClickListener(v -> saveMenuItem());
    }

    private void saveMenuItem() {
        // Validate inputs
        String name = etItemName.getText() != null ? etItemName.getText().toString().trim() : "";
        String description = etDescription.getText() != null ? etDescription.getText().toString().trim() : "";
        String priceStr = etPrice.getText() != null ? etPrice.getText().toString().trim() : "";
        String category = spinnerCategory.getText().toString().trim();
        String status = spinnerStatus.getText().toString().trim();
        String imageUrl = etImageUrl.getText() != null ? etImageUrl.getText().toString().trim() : "";

        // Validation
        if (TextUtils.isEmpty(name)) {
            etItemName.setError("Item name is required");
            etItemName.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(priceStr)) {
            etPrice.setError("Price is required");
            etPrice.requestFocus();
            return;
        }

        double price;
        try {
            price = Double.parseDouble(priceStr);
            if (price <= 0) {
                etPrice.setError("Price must be greater than 0");
                etPrice.requestFocus();
                return;
            }
        } catch (NumberFormatException e) {
            etPrice.setError("Invalid price format");
            etPrice.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(category)) {
            Toast.makeText(this, "Please select a category", Toast.LENGTH_SHORT).show();
            return;
        }

        if (TextUtils.isEmpty(status)) {
            Toast.makeText(this, "Please select a status", Toast.LENGTH_SHORT).show();
            return;
        }

        // Show loading state
        btnSave.setEnabled(false);
        btnSave.setText("Creating...");

        // Create menu item using real API service
        apiService.createMenuItem(name, description, price, category, imageUrl,
                true, status, new ApiService.MenuItemActionCallback() {
                    @Override
                    public void onSuccess(JSONObject result) {
                        runOnUiThread(() -> {
                            Toast.makeText(AddMenuItemActivity.this,
                                    "Menu item created successfully!", Toast.LENGTH_SHORT).show();
                            finish();
                        });
                    }

                    @Override
                    public void onError(String error) {
                        runOnUiThread(() -> {
                            btnSave.setEnabled(true);
                            btnSave.setText("Create Menu Item");
                            Toast.makeText(AddMenuItemActivity.this,
                                    "Error: " + error, Toast.LENGTH_LONG).show();
                        });
                    }
                });
    }
}