package com.example.coffeeshop.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.InventoryItem;
import java.util.ArrayList;
import java.util.List;

public class InventoryAdapter extends RecyclerView.Adapter<InventoryAdapter.InventoryViewHolder> {
    private List<InventoryItem> items = new ArrayList<>();

    public void updateItems(List<InventoryItem> newItems) {
        items.clear();
        items.addAll(newItems);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public InventoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_inventory, parent, false);
        return new InventoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull InventoryViewHolder holder, int position) {
        InventoryItem item = items.get(position);
        holder.bind(item);
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    static class InventoryViewHolder extends RecyclerView.ViewHolder {
        private TextView tvItemName, tvItemCategory, tvStockQuantity, tvStockUnit, tvStockStatus;

        public InventoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvItemName = itemView.findViewById(R.id.tv_item_name);
            tvItemCategory = itemView.findViewById(R.id.tv_item_category);
            tvStockQuantity = itemView.findViewById(R.id.tv_stock_quantity);
            tvStockUnit = itemView.findViewById(R.id.tv_stock_unit);
            tvStockStatus = itemView.findViewById(R.id.tv_stock_status);
        }

        public void bind(InventoryItem item) {
            tvItemName.setText(item.getName());
            tvItemCategory.setText(item.getCategory());
            tvStockQuantity.setText(String.valueOf(item.getQuantity()));
            tvStockUnit.setText(item.getUnit());

            // Show stock status
            if (item.getQuantity() == 0) {
                tvStockStatus.setVisibility(View.VISIBLE);
                tvStockStatus.setText("Out of Stock");
                tvStockStatus.setBackgroundColor(Color.parseColor("#F44336")); // Red
            } else if (item.getQuantity() <= item.getMinStock()) {
                tvStockStatus.setVisibility(View.VISIBLE);
                tvStockStatus.setText("Low Stock");
                tvStockStatus.setBackgroundColor(Color.parseColor("#FF9800")); // Orange
            } else {
                tvStockStatus.setVisibility(View.GONE);
            }
        }
    }
}
