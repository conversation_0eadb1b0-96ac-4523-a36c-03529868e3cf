package com.example.coffeeshop.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.models.RecipeStep;
import com.google.android.material.card.MaterialCardView;

import java.util.ArrayList;
import java.util.List;

public class RecipeStepAdapter extends RecyclerView.Adapter<RecipeStepAdapter.StepViewHolder> {

    private List<RecipeStep> steps;

    public RecipeStepAdapter() {
        this.steps = new ArrayList<>();
    }

    public void updateSteps(List<RecipeStep> newSteps) {
        this.steps.clear();
        if (newSteps != null) {
            this.steps.addAll(newSteps);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public StepViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_recipe_step, parent, false);
        return new StepViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull StepViewHolder holder, int position) {
        RecipeStep step = steps.get(position);
        holder.bind(step);
    }

    @Override
    public int getItemCount() {
        return steps.size();
    }

    static class StepViewHolder extends RecyclerView.ViewHolder {
        private MaterialCardView cardStep;
        private TextView tvStepNumber, tvStepInstruction, tvStepTip, tvStepDuration;

        public StepViewHolder(@NonNull View itemView) {
            super(itemView);
            cardStep = itemView.findViewById(R.id.card_step);
            tvStepNumber = itemView.findViewById(R.id.tv_step_number);
            tvStepInstruction = itemView.findViewById(R.id.tv_step_instruction);
            tvStepTip = itemView.findViewById(R.id.tv_step_tip);
            tvStepDuration = itemView.findViewById(R.id.tv_step_duration);
        }

        public void bind(RecipeStep step) {
            tvStepNumber.setText(String.valueOf(step.getStepNumber()));
            tvStepInstruction.setText(step.getInstruction());

            // Show/hide tip
            if (step.hasTip()) {
                tvStepTip.setText("💡 Tip: " + step.getTip());
                tvStepTip.setVisibility(View.VISIBLE);
            } else {
                tvStepTip.setVisibility(View.GONE);
            }

            // Show/hide duration
            if (step.hasTimer()) {
                tvStepDuration.setText("⏱ " + step.getFormattedDuration());
                tvStepDuration.setVisibility(View.VISIBLE);
            } else {
                tvStepDuration.setVisibility(View.GONE);
            }
        }
    }
}
