package com.example.coffeeshop.adapters;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.models.Order;
import com.example.coffeeshop.models.OrderItem;
import com.google.android.material.button.MaterialButton;

import java.util.Date;
import java.util.List;

public class OrderAdapter extends RecyclerView.Adapter<OrderAdapter.OrderViewHolder> {

    private Context context;
    private List<Order> orders;
    private OnOrderActionListener listener;

    public interface OnOrderActionListener {
        void onViewDetails(Order order);
        void onUpdateStatus(Order order);
    }

    public OrderAdapter(Context context, List<Order> orders) {
        this.context = context;
        this.orders = orders;
    }

    public void setOnOrderActionListener(OnOrderActionListener listener) {
        this.listener = listener;
    }

    public void updateOrders(List<Order> newOrders) {
        this.orders = newOrders;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public OrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_barista_order, parent, false);
        return new OrderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull OrderViewHolder holder, int position) {
        Order order = orders.get(position);
        holder.bind(order);
    }

    @Override
    public int getItemCount() {
        return orders.size();
    }

    class OrderViewHolder extends RecyclerView.ViewHolder {
        private TextView tvOrderId, tvStatusBadge, tvCustomerName, tvOrderTime;
        private TextView tvItemsSummary, tvTotalPrice;
        private MaterialButton btnUpdateStatus;

        public OrderViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvOrderId = itemView.findViewById(R.id.tv_order_id);
            tvStatusBadge = itemView.findViewById(R.id.tv_status_badge);
            tvCustomerName = itemView.findViewById(R.id.tv_customer_name);
            tvOrderTime = itemView.findViewById(R.id.tv_order_time);
            tvItemsSummary = itemView.findViewById(R.id.tv_items_summary);
            tvTotalPrice = itemView.findViewById(R.id.tv_total_price);
            btnUpdateStatus = itemView.findViewById(R.id.btn_update_status);
        }

        public void bind(Order order) {
            tvOrderId.setText("Order #" + order.getOrderId());
            tvStatusBadge.setText(order.getStatus().toUpperCase());
            tvCustomerName.setText("Customer: " + order.getCustomerName());
            tvOrderTime.setText(getTimeAgo(order));
            tvTotalPrice.setText("Total: " + order.getFormattedTotalPrice());

            // Set status badge color
            setStatusBadgeColor(order.getStatus());

            // Set items summary
            if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
                StringBuilder itemsSummary = new StringBuilder();
                for (int i = 0; i < order.getOrderItems().size(); i++) {
                    OrderItem item = order.getOrderItems().get(i);
                    if (i > 0) itemsSummary.append(", ");
                    itemsSummary.append(item.getQuantity()).append("x ").append(item.getItemName());
                }
                tvItemsSummary.setText(itemsSummary.toString());
            } else {
                tvItemsSummary.setText("No items");
            }

            // Set update button text based on status
            // Setup action button is now called after the click listener setup

            // Set click listener
            btnUpdateStatus.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onUpdateStatus(order);
                }
            });

            // Setup button based on order status
            setupUpdateButton(order);
        }        private void setStatusBadgeColor(String status) {
            GradientDrawable background = (GradientDrawable) tvStatusBadge.getBackground();
            if (background != null) {
                int color;
                switch (status.toLowerCase()) {
                    case "pending":
                        color = Color.parseColor("#FF9800"); // Orange
                        break;
                    case "preparing":
                        color = Color.parseColor("#2196F3"); // Blue
                        break;
                    case "finished":
                        color = Color.parseColor("#4CAF50"); // Green
                        break;
                    default:
                        color = Color.parseColor("#757575"); // Grey
                        break;
                }
                background.setColor(color);
            }
        }        private void setupUpdateButton(Order order) {
            String status = order.getStatus().toLowerCase();
            switch (status) {
                case "pending":
                    btnUpdateStatus.setText("Start Making");
                    btnUpdateStatus.setEnabled(true);
                    break;
                case "preparing":
                    btnUpdateStatus.setText("Mark Ready");
                    btnUpdateStatus.setEnabled(true);
                    break;
                case "ready":
                    btnUpdateStatus.setText("Complete Order");
                    btnUpdateStatus.setEnabled(true);
                    break;
                case "finished":
                case "completed":
                    btnUpdateStatus.setText("Completed");
                    btnUpdateStatus.setEnabled(false);
                    break;
                default:
                    btnUpdateStatus.setText("Process");
                    btnUpdateStatus.setEnabled(true);
                    break;
            }
        }private String getTimeAgo(Order order) {
            try {
                Date now = new Date();
                Date orderTime = order.getCreatedAt();
                
                long diffInMillis = now.getTime() - orderTime.getTime();
                long minutes = diffInMillis / (60 * 1000);
                
                if (minutes < 1) {
                    return "Just now";
                } else if (minutes < 60) {
                    return minutes + " min ago";
                } else {
                    long hours = minutes / 60;
                    return hours + " hr ago";
                }
            } catch (Exception e) {
                return "Recently";
            }
        }
    }
}
