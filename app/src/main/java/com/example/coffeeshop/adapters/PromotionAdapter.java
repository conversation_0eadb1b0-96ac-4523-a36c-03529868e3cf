package com.example.coffeeshop.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.Promotion;

import java.util.ArrayList;
import java.util.List;

public class PromotionAdapter extends RecyclerView.Adapter<PromotionAdapter.PromotionViewHolder> {

    private List<Promotion> promotions = new ArrayList<>();
    private Context context;
    private OnPromotionActionListener listener;

    public interface OnPromotionActionListener {
        void onPromotionDeleted(Promotion promotion, int position);
        void onPromotionEdit(Promotion promotion);
        void onPromotionStatusToggled(Promotion promotion, int position);
    }

    public PromotionAdapter(Context context, OnPromotionActionListener listener) {
        this.context = context;
        this.listener = listener;
    }

    public void updateList(List<Promotion> newPromotions) {
        promotions.clear();
        if (newPromotions != null) {
            promotions.addAll(newPromotions);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public PromotionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_promotion, parent, false);
        return new PromotionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PromotionViewHolder holder, int position) {
        Promotion promotion = promotions.get(position);

        // Bind data to views
        holder.tvPromotionCode.setText(promotion.getPromoCode() != null ? promotion.getPromoCode() : "No Code");
        holder.tvPromotionName.setText(promotion.getTitle());
        holder.tvPromotionValue.setText(promotion.getDiscountDisplayText());
        holder.tvDateRange.setText(promotion.getStartDate() + " - " + promotion.getEndDate());
        holder.tvApplicableProducts.setText(promotion.getApplicableProducts() != null ? 
            promotion.getApplicableProducts() : "All Products");
        holder.tvUsageCount.setText(promotion.getUsageDisplayText());

        // Map discount type to display text
        String displayType = getDisplayType(promotion.getDiscountType());
        holder.chipType.setText(displayType);

        String statusText = promotion.isActive() ? "Active" : "Inactive";
        holder.chipStatus.setText(statusText);

        // Set type chip appearance
        switch (promotion.getDiscountType()) {
            case "percentage":
                holder.chipType.setChipBackgroundColorResource(android.R.color.holo_blue_light);
                break;
            case "fixed_amount":
                holder.chipType.setChipBackgroundColorResource(android.R.color.holo_green_light);
                break;
            case "free_shipping":
                holder.chipType.setChipBackgroundColorResource(android.R.color.holo_orange_light);
                break;
        }

        // Set status chip appearance and toggle button text
        if (promotion.isActive()) {
            holder.chipStatus.setChipBackgroundColorResource(android.R.color.holo_green_light);
            holder.chipStatus.setTextColor(context.getResources().getColor(android.R.color.white));
            holder.btnToggleStatus.setText("Deactivate");
            holder.btnToggleStatus.setTextColor(context.getResources().getColor(android.R.color.holo_red_dark));
            holder.btnToggleStatus.setEnabled(true);
        } else {
            holder.chipStatus.setChipBackgroundColorResource(android.R.color.darker_gray);
            holder.chipStatus.setTextColor(context.getResources().getColor(android.R.color.white));
            holder.btnToggleStatus.setText("Activate");
            holder.btnToggleStatus.setTextColor(context.getResources().getColor(android.R.color.holo_green_dark));
            holder.btnToggleStatus.setEnabled(true);
        }

        // Handle button clicks
        holder.btnEdit.setOnClickListener(v -> {
            if (listener != null) {
                listener.onPromotionEdit(promotion);
            }
        });

        holder.btnToggleStatus.setOnClickListener(v -> {
            android.util.Log.d("PromotionAdapter", "=== TOGGLE BUTTON CLICKED ===");
            android.util.Log.d("PromotionAdapter", "Toggle button clicked for promotion: " + promotion.getTitle());
            android.util.Log.d("PromotionAdapter", "Promotion ID: " + promotion.getPromotionId());
            android.util.Log.d("PromotionAdapter", "Current status: " + promotion.isActive());
            android.util.Log.d("PromotionAdapter", "Button text: " + holder.btnToggleStatus.getText().toString());
            android.util.Log.d("PromotionAdapter", "Button enabled: " + holder.btnToggleStatus.isEnabled());
            android.util.Log.d("PromotionAdapter", "Button clickable: " + holder.btnToggleStatus.isClickable());
            android.util.Log.d("PromotionAdapter", "Position: " + position);
            android.util.Log.d("PromotionAdapter", "Listener is null: " + (listener == null));
            
            if (listener != null) {
                android.util.Log.d("PromotionAdapter", "Calling onPromotionStatusToggled");
                listener.onPromotionStatusToggled(promotion, position);
            } else {
                android.util.Log.e("PromotionAdapter", "Listener is null!");
            }
            android.util.Log.d("PromotionAdapter", "=== END TOGGLE BUTTON CLICK ===");
        });

        holder.btnDelete.setOnClickListener(v -> {
            showDeleteConfirmation(promotion, position);
        });
    }

    private String getDisplayType(String discountType) {
        switch (discountType) {
            case "percentage":
                return "Percentage";
            case "fixed_amount":
                return "Fixed Amount";
            case "free_shipping":
                return "Free Shipping";
            default:
                return "Unknown";
        }
    }

    @Override
    public int getItemCount() {
        return promotions.size();
    }

    private void showDeleteConfirmation(Promotion promotion, int position) {
        new AlertDialog.Builder(context)
                .setTitle("Delete Promotion")
                .setMessage("Are you sure you want to delete '" + promotion.getTitle() + "' promotion?\n\nThis action cannot be undone.")
                .setPositiveButton("Delete", (dialog, which) -> {
                    if (listener != null) {
                        listener.onPromotionDeleted(promotion, position);
                    }
                })
                .setNegativeButton("Cancel", null)
                .show();
    }

    static class PromotionViewHolder extends RecyclerView.ViewHolder {
        TextView tvPromotionCode, tvPromotionName, tvPromotionValue, tvDateRange, tvApplicableProducts, tvUsageCount;
        Chip chipType, chipStatus;
        MaterialButton btnEdit, btnToggleStatus, btnDelete;

        PromotionViewHolder(View itemView) {
            super(itemView);

            // Initialize views with correct IDs from item_promotion.xml
            tvPromotionCode = itemView.findViewById(R.id.tv_promotion_code);
            tvPromotionName = itemView.findViewById(R.id.tv_promotion_name);
            tvPromotionValue = itemView.findViewById(R.id.tv_promotion_value);
            tvDateRange = itemView.findViewById(R.id.tv_date_range);
            tvApplicableProducts = itemView.findViewById(R.id.tv_applicable_products);
            tvUsageCount = itemView.findViewById(R.id.tv_usage_count);
            chipType = itemView.findViewById(R.id.chip_promotion_type);
            chipStatus = itemView.findViewById(R.id.chip_promotion_status);
            btnEdit = itemView.findViewById(R.id.btn_edit_promotion);
            btnToggleStatus = itemView.findViewById(R.id.btn_toggle_status);
            btnDelete = itemView.findViewById(R.id.btn_delete_promotion);
        }
    }
}