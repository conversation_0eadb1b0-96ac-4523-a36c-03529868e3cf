package com.example.coffeeshop.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.DailyTask;
import java.util.ArrayList;
import java.util.List;

public class DailyTaskAdapter extends RecyclerView.Adapter<DailyTaskAdapter.TaskViewHolder> {
    private List<DailyTask> tasks = new ArrayList<>();
    private OnTaskToggleListener listener;

    public interface OnTaskToggleListener {
        void onTaskToggled(DailyTask task);
    }

    public DailyTaskAdapter(OnTaskToggleListener listener) {
        this.listener = listener;
    }

    public void updateTasks(List<DailyTask> newTasks) {
        tasks.clear();
        tasks.addAll(newTasks);
        // Use post to ensure we're not in a layout pass
        new android.os.Handler(android.os.Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                notifyDataSetChanged();
            }
        });
    }

    @NonNull
    @Override
    public TaskViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
            .inflate(R.layout.item_daily_task, parent, false);
        return new TaskViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TaskViewHolder holder, int position) {
        DailyTask task = tasks.get(position);
        holder.bind(task, listener);
    }

    @Override
    public int getItemCount() {
        return tasks.size();
    }

    static class TaskViewHolder extends RecyclerView.ViewHolder {
        private CheckBox cbTaskCompleted;
        private TextView tvTaskTitle, tvTaskDescription, tvTaskTime, tvPriority;

        public TaskViewHolder(@NonNull View itemView) {
            super(itemView);
            cbTaskCompleted = itemView.findViewById(R.id.cb_task_completed);
            tvTaskTitle = itemView.findViewById(R.id.tv_task_title);
            tvTaskDescription = itemView.findViewById(R.id.tv_task_description);
            tvTaskTime = itemView.findViewById(R.id.tv_task_time);
            tvPriority = itemView.findViewById(R.id.tv_priority);
        }

        public void bind(DailyTask task, OnTaskToggleListener listener) {
            tvTaskTitle.setText(task.getTitle());
            tvTaskDescription.setText(task.getDescription());
            tvTaskTime.setText(task.getEstimatedTime());
            
            // Clear any existing listener to prevent triggering during bind
            cbTaskCompleted.setOnCheckedChangeListener(null);
            cbTaskCompleted.setChecked(task.isCompleted());

            // Show priority badge for high priority tasks
            if ("high".equals(task.getPriority())) {
                tvPriority.setVisibility(View.VISIBLE);
                tvPriority.setText("High");
                tvPriority.setBackgroundColor(Color.parseColor("#F44336")); // Red
            } else if ("medium".equals(task.getPriority())) {
                tvPriority.setVisibility(View.VISIBLE);
                tvPriority.setText("Med");
                tvPriority.setBackgroundColor(Color.parseColor("#FF9800")); // Orange
            } else {
                tvPriority.setVisibility(View.GONE);
            }

            // Strike through completed tasks
            if (task.isCompleted()) {
                tvTaskTitle.setPaintFlags(tvTaskTitle.getPaintFlags() | android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
                tvTaskDescription.setAlpha(0.6f);
            } else {
                tvTaskTitle.setPaintFlags(tvTaskTitle.getPaintFlags() & ~android.graphics.Paint.STRIKE_THRU_TEXT_FLAG);
                tvTaskDescription.setAlpha(1.0f);
            }

            // Set the listener after setting the checked state
            cbTaskCompleted.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (listener != null) {
                    listener.onTaskToggled(task);
                }
            });
        }
    }
}
