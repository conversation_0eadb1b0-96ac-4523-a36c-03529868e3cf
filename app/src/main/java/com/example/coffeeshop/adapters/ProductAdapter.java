package com.example.coffeeshop.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.Product;

import java.util.List;

public class ProductAdapter extends RecyclerView.Adapter<ProductAdapter.ProductViewHolder> {

    private List<Product> products;
    private Context context;
    private OnAddToCartClickListener addToCartListener;

    public interface OnAddToCartClickListener {
        void onAddToCartClick(Product product);
    }

    public ProductAdapter(Context context, List<Product> products) {
        this.context = context;
        this.products = products;
    }

    public void setOnAddToCartClickListener(OnAddToCartClickListener listener) {
        this.addToCartListener = listener;
    }

    public void updateProducts(List<Product> newProducts) {
        this.products = newProducts;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ProductViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_product, parent, false);
        return new ProductViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ProductViewHolder holder, int position) {
        Product product = products.get(position);
        holder.bind(product);
    }

    @Override
    public int getItemCount() {
        return products.size();
    }

    class ProductViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivProductImage;
        private TextView tvProductName;
        private TextView tvProductDescription;
        private TextView tvProductCategory;
        private TextView tvProductPrice;
        private TextView tvStockStatus;
        private Button btnAddToCart;

        public ProductViewHolder(@NonNull View itemView) {
            super(itemView);
            ivProductImage = itemView.findViewById(R.id.iv_product_image);
            tvProductName = itemView.findViewById(R.id.tv_product_name);
            tvProductDescription = itemView.findViewById(R.id.tv_product_description);
            tvProductCategory = itemView.findViewById(R.id.tv_product_category);
            tvProductPrice = itemView.findViewById(R.id.tv_product_price);
            tvStockStatus = itemView.findViewById(R.id.tv_stock_status);
            btnAddToCart = itemView.findViewById(R.id.btn_add_to_cart);
        }

        public void bind(Product product) {
            tvProductName.setText(product.getName());
            tvProductDescription.setText(product.getDescription());
            tvProductCategory.setText(product.getCategory());
            tvProductPrice.setText(String.format("$%.2f", product.getPrice()));            // Set stock status with better visual feedback
            if (product.isAvailable() && product.getQuantityAvailable() > 0) {
                if (product.getQuantityAvailable() <= 5) {
                    tvStockStatus.setText(String.format("Low Stock (%d left)", product.getQuantityAvailable()));
                    tvStockStatus.setTextColor(ContextCompat.getColor(context, R.color.warning));
                } else {
                    tvStockStatus.setText("In Stock");
                    tvStockStatus.setTextColor(ContextCompat.getColor(context, R.color.success));
                }
                btnAddToCart.setEnabled(true);
                btnAddToCart.setText("Add to Cart");
                btnAddToCart.setAlpha(1.0f);
            } else {
                tvStockStatus.setText("Out of Stock");
                tvStockStatus.setTextColor(ContextCompat.getColor(context, R.color.error));
                btnAddToCart.setEnabled(false);
                btnAddToCart.setText("Unavailable");
                btnAddToCart.setAlpha(0.6f);
            }// Load product image using Glide
            if (product.getImageUrl() != null && !product.getImageUrl().isEmpty()) {
                Glide.with(context)
                        .load(product.getImageUrl())
                        .placeholder(R.drawable.ic_launcher_background)
                        .error(R.drawable.ic_launcher_background)
                        .transition(DrawableTransitionOptions.withCrossFade())
                        .into(ivProductImage);
            } else {
                // Fallback to placeholder if no image URL
                ivProductImage.setImageResource(R.drawable.ic_launcher_background);
            }

            btnAddToCart.setOnClickListener(v -> {
                if (addToCartListener != null && product.isAvailable()) {
                    addToCartListener.onAddToCartClick(product);
                } else {
                    Toast.makeText(context, "Product is currently unavailable", Toast.LENGTH_SHORT).show();
                }
            });
        }
    }
}
