package com.example.coffeeshop.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;

import org.json.JSONObject;

import java.util.ArrayList;

public class CompletedOrdersAdapter extends RecyclerView.Adapter<CompletedOrdersAdapter.ViewHolder> {
    private Context context;
    private ArrayList<JSONObject> orders;

    public CompletedOrdersAdapter(Context context, ArrayList<JSONObject> orders) {
        this.context = context;
        this.orders = orders;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_completed_order, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        JSONObject order = orders.get(position);
        try {
            holder.tvOrderId.setText("Order #" + order.optInt("id"));
            holder.tvCustomer.setText("Customer: " + order.optString("customer_name"));
            holder.tvTotal.setText("Total: $" + order.optDouble("total_price"));
            holder.tvCompletedAt.setText("Completed At: " + order.optString("completed_at"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return orders.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvOrderId, tvCustomer, tvTotal, tvCompletedAt;

        public ViewHolder(View itemView) {
            super(itemView);
            tvOrderId = itemView.findViewById(R.id.tv_order_id);
            tvCustomer = itemView.findViewById(R.id.tv_customer_name);
            tvTotal = itemView.findViewById(R.id.tv_total_price);
            tvCompletedAt = itemView.findViewById(R.id.tv_created_at);
        }
    }
}
