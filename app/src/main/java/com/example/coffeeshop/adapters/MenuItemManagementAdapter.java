package com.example.coffeeshop.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.chip.Chip;
import com.example.coffeeshop.R;
import com.example.coffeeshop.models.MenuItem;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class MenuItemManagementAdapter extends RecyclerView.Adapter<MenuItemManagementAdapter.MenuItemViewHolder> {

    private final List<MenuItem> menuItems = new ArrayList<>();
    private final Context context;
    private final OnMenuItemActionListener listener;

    public interface OnMenuItemActionListener {
        void onMenuItemDeleted(MenuItem menuItem, int position);
        void onMenuItemEdit(MenuItem menuItem);
        void onMenuItemAvailabilityToggled(MenuItem menuItem, int position);
        void onMenuItemStatusToggled(MenuItem menuItem, int position);
    }

    public MenuItemManagementAdapter(Context context, OnMenuItemActionListener listener) {
        this.context = context;
        this.listener = listener;
    }

    public void updateList(List<MenuItem> newMenuItems) {
        menuItems.clear();
        if (newMenuItems != null) {
            menuItems.addAll(newMenuItems);
        }
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public MenuItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_menu_item_management, parent, false);
        return new MenuItemViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull MenuItemViewHolder holder, int position) {
        MenuItem menuItem = menuItems.get(position);

        // Bind data to views
        holder.tvMenuItemName.setText(menuItem.getName());
        holder.tvMenuItemDescription.setText(menuItem.getDescription() != null ?
            menuItem.getDescription() : "No description");
        holder.tvMenuItemPrice.setText(String.format(Locale.getDefault(), "$%.2f", menuItem.getPrice()));
        holder.tvMenuItemCategory.setText(menuItem.getCategory());

        // Set category chip appearance
        holder.chipCategory.setText(menuItem.getCategory());
        setCategoryChipColor(holder.chipCategory, menuItem.getCategory());

        // Set availability chip
        String availabilityText = menuItem.isAvailable() ? "Available" : "Unavailable";
        holder.chipAvailability.setText(availabilityText);
        if (menuItem.isAvailable()) {
            holder.chipAvailability.setChipBackgroundColorResource(android.R.color.holo_green_light);
            holder.chipAvailability.setTextColor(ContextCompat.getColor(context, android.R.color.white));
            holder.btnToggleAvailability.setText(R.string.make_unavailable);
            holder.btnToggleAvailability.setTextColor(ContextCompat.getColor(context, android.R.color.holo_red_dark));
        } else {
            holder.chipAvailability.setChipBackgroundColorResource(android.R.color.darker_gray);
            holder.chipAvailability.setTextColor(ContextCompat.getColor(context, android.R.color.white));
            holder.btnToggleAvailability.setText(R.string.make_available);
            holder.btnToggleAvailability.setTextColor(ContextCompat.getColor(context, android.R.color.holo_green_dark));
        }

        // Set status chip
        holder.chipStatus.setText(menuItem.getStatus());
        if ("active".equalsIgnoreCase(menuItem.getStatus())) {
            holder.chipStatus.setChipBackgroundColorResource(android.R.color.holo_blue_light);
            holder.chipStatus.setTextColor(ContextCompat.getColor(context, android.R.color.white));
            holder.btnToggleStatus.setText(R.string.deactivate);
            holder.btnToggleStatus.setTextColor(ContextCompat.getColor(context, android.R.color.holo_red_dark));
        } else {
            holder.chipStatus.setChipBackgroundColorResource(android.R.color.darker_gray);
            holder.chipStatus.setTextColor(ContextCompat.getColor(context, android.R.color.white));
            holder.btnToggleStatus.setText(R.string.activate);
            holder.btnToggleStatus.setTextColor(ContextCompat.getColor(context, android.R.color.holo_green_dark));
        }

        // Load image (placeholder for now)
        // You can use Glide or Picasso to load actual images
        holder.ivMenuItemImage.setImageResource(android.R.drawable.ic_menu_gallery);

        // Handle button clicks
        holder.btnEdit.setOnClickListener(v -> {
            if (listener != null) {
                listener.onMenuItemEdit(menuItem);
            }
        });

        holder.btnToggleAvailability.setOnClickListener(v -> {
            if (listener != null) {
                listener.onMenuItemAvailabilityToggled(menuItem, position);
            }
        });

        holder.btnToggleStatus.setOnClickListener(v -> {
            if (listener != null) {
                listener.onMenuItemStatusToggled(menuItem, position);
            }
        });

        holder.btnDelete.setOnClickListener(v -> showDeleteConfirmation(menuItem, position));
    }

    private void setCategoryChipColor(Chip chip, String category) {
        switch (category.toLowerCase()) {
            case "hot coffee":
                chip.setChipBackgroundColorResource(android.R.color.holo_red_light);
                break;
            case "cold coffee":
                chip.setChipBackgroundColorResource(android.R.color.holo_blue_light);
                break;
            case "tea":
                chip.setChipBackgroundColorResource(android.R.color.holo_green_light);
                break;
            case "snacks":
                chip.setChipBackgroundColorResource(android.R.color.holo_orange_light);
                break;
            case "desserts":
                chip.setChipBackgroundColorResource(android.R.color.holo_purple);
                break;
            default:
                chip.setChipBackgroundColorResource(android.R.color.darker_gray);
                break;
        }
    }

    @Override
    public int getItemCount() {
        return menuItems.size();
    }

    private void showDeleteConfirmation(MenuItem menuItem, int position) {
        new AlertDialog.Builder(context)
                .setTitle("Delete Menu Item")
                .setMessage("Are you sure you want to delete '" + menuItem.getName() + "'?\n\nThis action cannot be undone.")
                .setPositiveButton("Delete", (dialog, which) -> {
                    if (listener != null) {
                        listener.onMenuItemDeleted(menuItem, position);
                    }
                })
                .setNegativeButton("Cancel", null)
                .show();
    }

    static class MenuItemViewHolder extends RecyclerView.ViewHolder {
        ImageView ivMenuItemImage;
        TextView tvMenuItemName, tvMenuItemDescription, tvMenuItemPrice, tvMenuItemCategory;
        Chip chipCategory, chipAvailability, chipStatus;
        MaterialButton btnEdit, btnToggleAvailability, btnToggleStatus, btnDelete;

        MenuItemViewHolder(View itemView) {
            super(itemView);

            ivMenuItemImage = itemView.findViewById(R.id.iv_menu_item_image);
            tvMenuItemName = itemView.findViewById(R.id.tv_menu_item_name);
            tvMenuItemDescription = itemView.findViewById(R.id.tv_menu_item_description);
            tvMenuItemPrice = itemView.findViewById(R.id.tv_menu_item_price);
            tvMenuItemCategory = itemView.findViewById(R.id.tv_menu_item_category);
            chipCategory = itemView.findViewById(R.id.chip_category);
            chipAvailability = itemView.findViewById(R.id.chip_availability);
            chipStatus = itemView.findViewById(R.id.chip_status);
            btnEdit = itemView.findViewById(R.id.btn_edit_menu_item);
            btnToggleAvailability = itemView.findViewById(R.id.btn_toggle_availability);
            btnToggleStatus = itemView.findViewById(R.id.btn_toggle_status);
            btnDelete = itemView.findViewById(R.id.btn_delete_menu_item);
        }
    }
}