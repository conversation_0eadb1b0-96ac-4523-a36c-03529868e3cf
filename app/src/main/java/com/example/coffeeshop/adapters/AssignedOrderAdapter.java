package com.example.coffeeshop.adapters;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.coffeeshop.R;
import com.example.coffeeshop.activities.ShipperOrderDetailsActivity;
import com.example.coffeeshop.models.Order;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class AssignedOrderAdapter extends RecyclerView.Adapter<AssignedOrderAdapter.OrderViewHolder> {

    private final Context context;
    private final List<Order> orders;
    private final OnOrderClickListener listener;
    private final SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());

    public interface OnOrderClickListener {
        void onOrderClick(Order order);
    }

    public AssignedOrderAdapter(Context context, List<Order> orders, OnOrderClickListener listener) {
        this.context = context;
        this.orders = orders;
        this.listener = listener;
    }

    @NonNull
    @Override
    public OrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_assigned_order, parent, false);
        return new OrderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull OrderViewHolder holder, int position) {
        Order order = orders.get(position);

        holder.tvOrderId.setText("Order #" + order.getOrderId());
        holder.tvCustomerName.setText(order.getCustomerName());
        holder.tvDeliveryAddress.setText(order.getDeliveryAddress());
        holder.tvTotalPrice.setText(String.format(Locale.getDefault(), "$%.2f", order.getTotalPrice().doubleValue()));

        String status = order.getStatus() != null ? order.getStatus().toLowerCase() : "";
        holder.tvStatus.setText(capitalize(status));

        switch (status) {
            case "pending":
                holder.tvStatus.setTextColor(Color.parseColor("#FFA000")); // Amber
                break;
            case "completed":
                holder.tvStatus.setTextColor(Color.parseColor("#4CAF50")); // Green
                break;
            default:
                holder.tvStatus.setTextColor(Color.GRAY);
                break;
        }

        if (order.getCreatedAt() != null) {
            holder.tvCreatedAt.setText(sdf.format(order.getCreatedAt()));
        } else {
            holder.tvCreatedAt.setText("-");
        }

        holder.itemView.setOnClickListener(v -> {
            if (listener != null) listener.onOrderClick(order);
        });

        holder.btnOrderDetail.setOnClickListener(v -> {
            Intent intent = new Intent(context, ShipperOrderDetailsActivity.class);
            intent.putExtra("order_id", order.getOrderId());
            context.startActivity(intent);
        });
    }

    @Override
    public int getItemCount() {
        return orders.size();
    }


    public void removeItem(int position) {
        if (position >= 0 && position < orders.size()) {
            orders.remove(position);
            notifyItemRemoved(position);
        }
    }

    public static class OrderViewHolder extends RecyclerView.ViewHolder {
        TextView tvOrderId, tvCustomerName, tvDeliveryAddress, tvTotalPrice, tvStatus, tvCreatedAt, btnOrderDetail;

        public OrderViewHolder(@NonNull View itemView) {
            super(itemView);
            tvOrderId = itemView.findViewById(R.id.tv_order_id);
            tvCustomerName = itemView.findViewById(R.id.tv_customer_name);
            tvDeliveryAddress = itemView.findViewById(R.id.tv_delivery_address);
            tvTotalPrice = itemView.findViewById(R.id.tv_total_price);
            tvStatus = itemView.findViewById(R.id.tv_order_status);
            tvCreatedAt = itemView.findViewById(R.id.tv_created_at);
            btnOrderDetail = itemView.findViewById(R.id.btn_order_detail);
        }
    }

    private String capitalize(String s) {
        if (s == null || s.isEmpty()) return "";
        return s.substring(0, 1).toUpperCase() + s.substring(1);
    }
}
