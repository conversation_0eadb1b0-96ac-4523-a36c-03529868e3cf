package com.example.coffeeshop.api;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ApiService {
    private static final String BASE_URL_EMULATOR = "http://********:3000/api"; // Android emulator localhost
    private static final String BASE_URL_LOCALHOST = "http://localhost:3000/api"; // Direct localhost
    private static final String TAG = "ApiService";
    private ExecutorService executor;
    private Handler mainHandler;
    
    public ApiService() {
        executor = Executors.newCachedThreadPool();
        mainHandler = new Handler(Looper.getMainLooper());
    }

    // ==================== AUTH METHODS ====================
    
    public interface AuthCallback {
        void onSuccess(JSONObject user);
        void onError(String error);
    }

    public void login(String email, String password, AuthCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("email", email);
                requestBody.put("password", password);

                JSONObject response = makeRequest("POST", "/auth/login", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Login failed");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Login error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void register(String fullName, String email, String phone, String password, AuthCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("full_name", fullName);
                requestBody.put("email", email);
                requestBody.put("phone", phone);
                requestBody.put("password", password);

                JSONObject response = makeRequest("POST", "/auth/register", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Registration failed");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Register error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== USER PROFILE METHODS ====================
    
    public interface UserCallback {
        void onSuccess(JSONObject user);
        void onError(String error);
    }
    
    public interface ProfileUpdateCallback {
        void onSuccess(JSONObject response);
        void onError(String error);
    }
    
    public void getUserProfile(int userId, UserCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/auth/profile/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            callback.onSuccess(response.getJSONObject("user"));
                        } catch (Exception e) {
                            callback.onError("Invalid response format");
                        }
                    } else {
                        String message = response != null ? response.optString("message", "Failed to get profile") : "Failed to get profile";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get user profile error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    
    public void updateUserProfile(int userId, String fullName, String email, String phone, ProfileUpdateCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("full_name", fullName);
                requestBody.put("email", email);
                requestBody.put("phone", phone);

                JSONObject response = makeRequest("PUT", "/auth/profile/" + userId, requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to update profile") : "Failed to update profile";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update user profile error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    
    public void changePassword(int userId, String currentPassword, String newPassword, ProfileUpdateCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("current_password", currentPassword);
                requestBody.put("new_password", newPassword);

                JSONObject response = makeRequest("PUT", "/auth/change-password/" + userId, requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to change password") : "Failed to change password";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Change password error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== PRODUCTS METHODS ====================

    public interface ProductsCallback {
        void onSuccess(JSONArray products);
        void onError(String error);
    }

    public interface CategoriesCallback {
        void onSuccess(JSONArray categories);
        void onError(String error);
    }

    public void getProducts(ProductsCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/products", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load products");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get products error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getProductsByCategory(String category, ProductsCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/products/category/" + category, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load products");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get products by category error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getCategories(CategoriesCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/products/categories", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load categories");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get categories error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== CART METHODS ====================

    public interface CartCallback {
        void onSuccess(JSONArray cartItems);
        void onError(String error);
    }

    public interface CartActionCallback {
        void onSuccess(String message);
        void onError(String error);
    }

    public void getCartItems(int userId, CartCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/cart/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void addToCart(int userId, int productId, int quantity, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Adding to cart: userId=" + userId + ", productId=" + productId + ", quantity=" + quantity);
                
                JSONObject requestBody = new JSONObject();
                requestBody.put("user_id", userId);
                requestBody.put("product_id", productId);
                requestBody.put("quantity", quantity);

                Log.d(TAG, "Request URL: " + BASE_URL_EMULATOR + "/cart");
                Log.d(TAG, "Request body: " + requestBody.toString());

                JSONObject response = makeRequest("POST", "/cart", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        Log.d(TAG, "Add to cart success: " + response.toString());
                        callback.onSuccess(response.optString("message", "Added to cart"));
                    } else {
                        Log.e(TAG, "Add to cart failed: response is null");
                        callback.onError("Failed to add to cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Add to cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateCartItem(int cartId, int quantity, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("quantity", quantity);

                JSONObject response = makeRequest("PUT", "/cart/" + cartId, requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response.optString("message", "Cart updated"));
                    } else {
                        callback.onError("Failed to update cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void removeFromCart(int cartId, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("DELETE", "/cart/" + cartId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response.optString("message", "Item removed"));
                    } else {
                        callback.onError("Failed to remove item");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Remove from cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void clearCart(int userId, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("DELETE", "/cart/user/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response.optString("message", "Cart cleared"));
                    } else {
                        callback.onError("Failed to clear cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Clear cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== ORDER METHODS ====================

    public interface OrdersCallback {
        void onSuccess(JSONArray orders);
        void onError(String error);
    }

    public interface OrderCallback {
        void onSuccess(JSONObject order);
        void onError(String error);
    }

    public interface OrderActionCallback {
        void onSuccess(JSONObject result);
        void onError(String error);
    }

    public void getUserOrders(int userId, OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders/user/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load orders");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get orders error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getOrder(int orderId, OrderCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/orders/" + orderId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load order");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get order error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void createOrder(int customerId, String customerName, double totalPrice, 
                           String deliveryAddress, String specialInstructions, 
                           String paymentMethod, JSONArray items, OrderActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("customer_id", customerId);
                requestBody.put("customer_name", customerName);
                requestBody.put("total_price", totalPrice);
                requestBody.put("delivery_address", deliveryAddress);
                requestBody.put("special_instructions", specialInstructions);
                requestBody.put("payment_method", paymentMethod);
                requestBody.put("items", items);

                JSONObject response = makeRequest("POST", "/orders", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to create order");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Create order error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateOrderStatus(int orderId, String status, OrderActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("status", status);

                JSONObject response = makeRequest("PUT", "/orders/" + orderId + "/status", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to update order status");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update order status error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    
        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    
        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    
        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    
        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== PRIVATE HELPER METHODS ====================

    private JSONObject makeRequest(String method, String endpoint, JSONObject requestBody) {
        try {
            URL url = new URL(BASE_URL_EMULATOR + endpoint);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

                connection.setDoOutput(true);
                OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream());
                writer.write(requestBody.toString());
                writer.flush();
                writer.close();
            }

            int responseCode = connection.getResponseCode();
            BufferedReader reader;
            
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            if (responseCode >= 200 && responseCode < 300) {
                return new JSONObject(response.toString());
            } else {
                Log.e(TAG, "Request failed with code: " + responseCode + ", response: " + response.toString());
                return null;
            }

        } catch (IOException | JSONException e) {
            Log.e(TAG, "Request error", e);
            return null;
        }
    }

    private JSONArray makeArrayRequest(String method, String endpoint, JSONObject requestBody) {
        try {
            URL url = new URL(BASE_URL_EMULATOR + endpoint);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

                connection.setDoOutput(true);
                OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream());
                writer.write(requestBody.toString());
                writer.flush();
                writer.close();
            }

            int responseCode = connection.getResponseCode();
            BufferedReader reader;
            
            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                return null;
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            return new JSONArray(response.toString());

        } catch (IOException | JSONException e) {
            Log.e(TAG, "Array request error", e);
            return null;
        }
    }


        void onError(String error);
    }

        void onError(String error);
    }

        void onError(String error);
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();

                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();

                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

        executor.execute(() -> {
            try {
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
} 