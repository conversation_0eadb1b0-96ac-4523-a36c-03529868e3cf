package com.example.coffeeshop.api;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ApiService {
    private static final String BASE_URL_EMULATOR = "http://********:3000/api"; // Android emulator localhost
    private static final String BASE_URL_LOCALHOST = "http://localhost:3000/api"; // Direct localhost
    private static final String TAG = "ApiService";
    private ExecutorService executor;
    private Handler mainHandler;

    public ApiService() {
        executor = Executors.newCachedThreadPool();
        mainHandler = new Handler(Looper.getMainLooper());
    }

    // ==================== AUTH METHODS ====================

    public interface AuthCallback {
        void onSuccess(JSONObject user);

        void onError(String error);
    }

    public void login(String email, String password, AuthCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("email", email);
                requestBody.put("password", password);

                JSONObject response = makeRequest("POST", "/auth/login", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Login failed");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Login error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void register(String fullName, String email, String phone, String password, AuthCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("full_name", fullName);
                requestBody.put("email", email);
                requestBody.put("phone", phone);
                requestBody.put("password", password);

                JSONObject response = makeRequest("POST", "/auth/register", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Registration failed");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Register error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== USER PROFILE METHODS ====================

    public interface UserCallback {
        void onSuccess(JSONObject user);

        void onError(String error);
    }

    public interface ProfileUpdateCallback {
        void onSuccess(JSONObject response);

        void onError(String error);
    }

    public void getUserProfile(int userId, UserCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/auth/profile/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            callback.onSuccess(response.getJSONObject("user"));
                        } catch (Exception e) {
                            callback.onError("Invalid response format");
                        }
                    } else {
                        String message = response != null ? response.optString("message", "Failed to get profile") : "Failed to get profile";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get user profile error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateUserProfile(int userId, String fullName, String email, String phone, ProfileUpdateCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("full_name", fullName);
                requestBody.put("email", email);
                requestBody.put("phone", phone);

                JSONObject response = makeRequest("PUT", "/auth/profile/" + userId, requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to update profile") : "Failed to update profile";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update user profile error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void changePassword(int userId, String currentPassword, String newPassword, ProfileUpdateCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("current_password", currentPassword);
                requestBody.put("new_password", newPassword);

                JSONObject response = makeRequest("PUT", "/auth/change-password/" + userId, requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to change password") : "Failed to change password";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Change password error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== PRODUCTS METHODS ====================

    public interface ProductsCallback {
        void onSuccess(JSONArray products);

        void onError(String error);
    }

    public interface CategoriesCallback {
        void onSuccess(JSONArray categories);

        void onError(String error);
    }

    public void getProducts(ProductsCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/products", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load products");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get products error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getProductsByCategory(String category, ProductsCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/products/category/" + category, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load products");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get products by category error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getCategories(CategoriesCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/products/categories", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load categories");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get categories error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== CART METHODS ====================

    public interface CartCallback {
        void onSuccess(JSONArray cartItems);

        void onError(String error);
    }

    public interface CartActionCallback {
        void onSuccess(String message);

        void onError(String error);
    }

    public void getCartItems(int userId, CartCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/cart/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void addToCart(int userId, int productId, int quantity, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Adding to cart: userId=" + userId + ", productId=" + productId + ", quantity=" + quantity);

                JSONObject requestBody = new JSONObject();
                requestBody.put("user_id", userId);
                requestBody.put("product_id", productId);
                requestBody.put("quantity", quantity);

                Log.d(TAG, "Request URL: " + BASE_URL_EMULATOR + "/cart");
                Log.d(TAG, "Request body: " + requestBody.toString());

                JSONObject response = makeRequest("POST", "/cart", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        Log.d(TAG, "Add to cart success: " + response.toString());
                        callback.onSuccess(response.optString("message", "Added to cart"));
                    } else {
                        Log.e(TAG, "Add to cart failed: response is null");
                        callback.onError("Failed to add to cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Add to cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateCartItem(int cartId, int quantity, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("quantity", quantity);

                JSONObject response = makeRequest("PUT", "/cart/" + cartId, requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response.optString("message", "Cart updated"));
                    } else {
                        callback.onError("Failed to update cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void removeFromCart(int cartId, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("DELETE", "/cart/" + cartId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response.optString("message", "Item removed"));
                    } else {
                        callback.onError("Failed to remove item");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Remove from cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void clearCart(int userId, CartActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("DELETE", "/cart/user/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response.optString("message", "Cart cleared"));
                    } else {
                        callback.onError("Failed to clear cart");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Clear cart error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== ORDER METHODS ====================

    public interface OrdersCallback {
        void onSuccess(JSONArray orders);

        void onError(String error);
    }

    public interface OrderCallback {
        void onSuccess(JSONObject order);

        void onError(String error);
    }

    public interface OrderActionCallback {
        void onSuccess(JSONObject result);

        void onError(String error);
    }

    public void getUserOrders(int userId, OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders/user/" + userId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load orders");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get orders error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getOrder(int orderId, OrderCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/orders/" + orderId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load order");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get order error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void createOrder(int customerId, String customerName, double totalPrice,
                            String deliveryAddress, String specialInstructions,
                            String paymentMethod, JSONArray items, OrderActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("customer_id", customerId);
                requestBody.put("customer_name", customerName);
                requestBody.put("total_price", totalPrice);
                requestBody.put("delivery_address", deliveryAddress);
                requestBody.put("special_instructions", specialInstructions);
                requestBody.put("payment_method", paymentMethod);
                requestBody.put("items", items);

                JSONObject response = makeRequest("POST", "/orders", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to create order");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Create order error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateOrderStatus(int orderId, String status, OrderActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("status", status);

                JSONObject response = makeRequest("PUT", "/orders/" + orderId + "/status", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to update order status");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update order status error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== PROMOTION METHODS ====================

    public interface PromotionsCallback {
        void onSuccess(JSONArray promotions);

        void onError(String error);
    }

    public interface PromotionCallback {
        void onSuccess(JSONObject promotion);

        void onError(String error);
    }

    public interface PromotionActionCallback {
        void onSuccess(JSONObject result);

        void onError(String error);
    }

    public interface PromotionValidationCallback {
        void onSuccess(JSONObject promotion);

        void onError(String error);
    }

    public void getPromotions(String active, String search, String discountType, PromotionsCallback callback) {
        executor.execute(() -> {
            try {
                StringBuilder urlBuilder = new StringBuilder("/promotions");
                boolean hasParams = false;

                if (active != null) {
                    urlBuilder.append(hasParams ? "&" : "?").append("active=").append(active);
                    hasParams = true;
                }
                if (search != null && !search.trim().isEmpty()) {
                    urlBuilder.append(hasParams ? "&" : "?").append("search=").append(search);
                    hasParams = true;
                }
                if (discountType != null && !discountType.trim().isEmpty()) {
                    urlBuilder.append(hasParams ? "&" : "?").append("discount_type=").append(discountType);
                }

                JSONObject response = makeRequest("GET", urlBuilder.toString(), null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            callback.onSuccess(response.getJSONArray("data"));
                        } catch (Exception e) {
                            callback.onError("Invalid response format");
                        }
                    } else {
                        String message = response != null ? response.optString("message", "Failed to load promotions") : "Failed to load promotions";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get promotions error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getPromotion(int promotionId, PromotionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/promotions/" + promotionId, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            callback.onSuccess(response.getJSONObject("data"));
                        } catch (Exception e) {
                            callback.onError("Invalid response format");
                        }
                    } else {
                        String message = response != null ? response.optString("message", "Failed to load promotion") : "Failed to load promotion";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get promotion error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void validatePromoCode(String promoCode, double orderAmount, PromotionValidationCallback callback) {
        executor.execute(() -> {
            try {
                String url = "/promotions/validate/" + promoCode + "?orderAmount=" + orderAmount;
                JSONObject response = makeRequest("GET", url, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            callback.onSuccess(response.getJSONObject("data"));
                        } catch (Exception e) {
                            callback.onError("Invalid response format");
                        }
                    } else {
                        String message = response != null ? response.optString("message", "Invalid promo code") : "Invalid promo code";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Validate promo code error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void createPromotion(String title, String description, String discountType, double discountValue,
                                double minimumOrderAmount, String startDate, String endDate, boolean isActive,
                                Integer usageLimit, String applicableProducts, String promoCode, PromotionActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("title", title);
                requestBody.put("description", description);
                requestBody.put("discount_type", discountType);
                requestBody.put("discount_value", discountValue);
                requestBody.put("minimum_order_amount", minimumOrderAmount);
                requestBody.put("start_date", startDate);
                requestBody.put("end_date", endDate);
                requestBody.put("is_active", isActive ? 1 : 0);
                if (usageLimit != null) {
                    requestBody.put("usage_limit", usageLimit);
                }
                if (applicableProducts != null) {
                    requestBody.put("applicable_products", applicableProducts);
                }
                if (promoCode != null) {
                    requestBody.put("promo_code", promoCode);
                }

                JSONObject response = makeRequest("POST", "/promotions", requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to create promotion") : "Failed to create promotion";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Create promotion error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updatePromotion(int promotionId, String title, String description, String discountType,
                                double discountValue, double minimumOrderAmount, String startDate, String endDate,
                                boolean isActive, Integer usageLimit, String applicableProducts, String promoCode,
                                PromotionActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("title", title);
                requestBody.put("description", description);
                requestBody.put("discount_type", discountType);
                requestBody.put("discount_value", discountValue);
                requestBody.put("minimum_order_amount", minimumOrderAmount);
                requestBody.put("start_date", startDate);
                requestBody.put("end_date", endDate);
                requestBody.put("is_active", isActive ? 1 : 0); // Convert boolean to integer
                if (usageLimit != null) {
                    requestBody.put("usage_limit", usageLimit);
                }
                if (applicableProducts != null) {
                    requestBody.put("applicable_products", applicableProducts);
                }
                if (promoCode != null) {
                    requestBody.put("promo_code", promoCode);
                }

                Log.d(TAG, "Updating promotion ID: " + promotionId + " with data: " + requestBody.toString());

                JSONObject response = makeRequest("PUT", "/promotions/" + promotionId, requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully updated promotion");
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to update promotion") : "Failed to update promotion";
                        Log.e(TAG, "Failed to update promotion: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update promotion error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void togglePromotionStatus(int promotionId, boolean isActive, PromotionActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("is_active", isActive ? 1 : 0); // Convert boolean to integer for backend

                Log.d(TAG, "Toggling promotion status for ID: " + promotionId + " to " + (isActive ? "active" : "inactive"));

                JSONObject response = makeRequest("PATCH", "/promotions/" + promotionId + "/status", requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully toggled promotion status");
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to update promotion status") : "Failed to update promotion status";
                        Log.e(TAG, "Failed to toggle promotion status: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Toggle promotion status error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void deletePromotion(int promotionId, PromotionActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("DELETE", "/promotions/" + promotionId, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to delete promotion") : "Failed to delete promotion";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Delete promotion error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void usePromotion(int promotionId, PromotionActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("PATCH", "/promotions/" + promotionId + "/use", null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("message", "Failed to use promotion") : "Failed to use promotion";
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Use promotion error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // ==================== PRIVATE HELPER METHODS ====================

    private JSONObject makeRequest(String method, String endpoint, JSONObject requestBody) {
        try {
            URL url = new URL(BASE_URL_EMULATOR + endpoint);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            if (requestBody != null && (method.equals("POST") || method.equals("PUT") || method.equals("PATCH"))) {
                connection.setDoOutput(true);
                OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream());
                writer.write(requestBody.toString());
                writer.flush();
                writer.close();
            }

            int responseCode = connection.getResponseCode();
            BufferedReader reader;

            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            if (responseCode >= 200 && responseCode < 300) {
                return new JSONObject(response.toString());
            } else {
                Log.e(TAG, "Request failed with code: " + responseCode + ", response: " + response.toString());
                try {
                    // Try to parse error response as JSON
                    return new JSONObject(response.toString());
                } catch (JSONException e) {
                    return null;
                }
            }

        } catch (IOException | JSONException e) {
            Log.e(TAG, "Request error", e);
            return null;
        }
    }

    private JSONArray makeArrayRequest(String method, String endpoint, JSONObject requestBody) {
        try {
            URL url = new URL(BASE_URL_EMULATOR + endpoint);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(method);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            if (requestBody != null && (method.equals("POST") || method.equals("PUT") || method.equals("PATCH"))) {
                connection.setDoOutput(true);
                OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream());
                writer.write(requestBody.toString());
                writer.flush();
                writer.close();
            }

            int responseCode = connection.getResponseCode();
            BufferedReader reader;

            if (responseCode >= 200 && responseCode < 300) {
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            } else {
                reader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                return null;
            }

            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();

            return new JSONArray(response.toString());

        } catch (IOException | JSONException e) {
            Log.e(TAG, "Array request error", e);
            return null;
        }
    }

    // ==================== MENU ITEM MANAGEMENT METHODS ====================

    // Menu Item callback interfaces
    public interface MenuItemCallback {
        void onSuccess(JSONObject menuItemData);
        void onError(String error);
    }

    public interface MenuItemsCallback {
        void onSuccess(JSONArray menuItems);
        void onError(String error);
    }

    public interface MenuItemActionCallback {
        void onSuccess(JSONObject result);
        void onError(String error);
    }

    public void getMenuItems(MenuItemsCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/menu-items", null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            JSONArray menuItemsArray = response.getJSONArray("data");
                            Log.d(TAG, "Successfully fetched " + menuItemsArray.length() + " menu items");
                            callback.onSuccess(menuItemsArray);
                        } catch (Exception e) {
                            Log.e(TAG, "Failed to parse menu items response", e);
                            callback.onError("Failed to parse response: " + e.getMessage());
                        }
                    } else {
                        String message = response != null ? response.optString("error", "Failed to fetch menu items") : "Failed to fetch menu items";
                        Log.e(TAG, "Failed to get menu items: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get menu items error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getMenuItem(int menuItemId, MenuItemCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/menu-items/" + menuItemId, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        JSONObject menuItemData = response.optJSONObject("data");
                        if (menuItemData != null) {
                            Log.d(TAG, "Successfully fetched menu item: " + menuItemId);
                            callback.onSuccess(menuItemData);
                        } else {
                            Log.e(TAG, "Menu item data not found in response");
                            callback.onError("Menu item data not found");
                        }
                    } else {
                        String message = response != null ? response.optString("error", "Failed to fetch menu item") : "Failed to fetch menu item";
                        Log.e(TAG, "Failed to get menu item: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get menu item error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getMenuCategories(MenuItemsCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/menu-items/categories/list", null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            JSONArray categories = response.getJSONArray("data");
                            Log.d(TAG, "Successfully fetched " + categories.length() + " categories");
                            callback.onSuccess(categories);
                        } catch (Exception e) {
                            Log.e(TAG, "Failed to parse categories response", e);
                            callback.onError("Invalid response format");
                        }
                    } else {
                        String message = response != null ? response.optString("error", "Failed to fetch menu categories") : "Failed to fetch menu categories";
                        Log.e(TAG, "Failed to get categories: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get menu categories error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void createMenuItem(String name, String description, double price, String category,
                               String imageUrl, boolean available, String status, MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("name", name);
                if (description != null && !description.trim().isEmpty()) {
                    requestBody.put("description", description);
                }
                requestBody.put("price", price);
                requestBody.put("category", category);
                if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                    requestBody.put("image_url", imageUrl);
                }
                requestBody.put("available", available);
                requestBody.put("quantity_available", 0); // Default quantity

                Log.d(TAG, "Creating menu item with data: " + requestBody.toString());

                JSONObject response = makeRequest("POST", "/menu-items", requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully created menu item: " + name);
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to create menu item") : "Failed to create menu item";
                        Log.e(TAG, "Failed to create menu item: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Create menu item error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateMenuItem(int menuItemId, String name, String description, double price, String category,
                               String imageUrl, boolean available, String status, MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                if (name != null && !name.trim().isEmpty()) {
                    requestBody.put("name", name);
                }
                if (description != null) {
                    requestBody.put("description", description);
                }
                if (price > 0) {
                    requestBody.put("price", price);
                }
                if (category != null && !category.trim().isEmpty()) {
                    requestBody.put("category", category);
                }
                if (imageUrl != null) {
                    requestBody.put("image_url", imageUrl);
                }
                requestBody.put("available", available);

                Log.d(TAG, "Updating menu item ID: " + menuItemId + " with data: " + requestBody.toString());

                JSONObject response = makeRequest("PUT", "/menu-items/" + menuItemId, requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully updated menu item: " + menuItemId);
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to update menu item") : "Failed to update menu item";
                        Log.e(TAG, "Failed to update menu item: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update menu item error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void toggleMenuItemAvailability(int menuItemId, boolean available, MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("available", available);

                Log.d(TAG, "Toggling menu item availability for ID: " + menuItemId + " to " + (available ? "available" : "unavailable"));

                JSONObject response = makeRequest("PATCH", "/menu-items/" + menuItemId + "/availability", requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully toggled menu item availability: " + menuItemId);
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to update menu item availability") : "Failed to update menu item availability";
                        Log.e(TAG, "Failed to toggle menu item availability: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Toggle menu item availability error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateMenuItemStatus(int menuItemId, String status, MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("status", status);

                Log.d(TAG, "Updating menu item status for ID: " + menuItemId + " to " + status);

                JSONObject response = makeRequest("PATCH", "/menu-items/" + menuItemId + "/status", requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully updated menu item status: " + menuItemId);
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to update menu item status") : "Failed to update menu item status";
                        Log.e(TAG, "Failed to update menu item status: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update menu item status error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void deleteMenuItem(int menuItemId, MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Deleting menu item ID: " + menuItemId);

                JSONObject response = makeRequest("DELETE", "/menu-items/" + menuItemId, null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully deleted menu item: " + menuItemId);
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to delete menu item") : "Failed to delete menu item";
                        Log.e(TAG, "Failed to delete menu item: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Delete menu item error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getMenuItemsStatistics(MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/menu-items/stats/overview", null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully fetched menu items statistics");
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to get menu items statistics") : "Failed to get menu items statistics";
                        Log.e(TAG, "Failed to get statistics: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get menu items statistics error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void bulkUpdateMenuItemAvailability(int[] menuItemIds, boolean available, MenuItemActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                JSONArray idsArray = new JSONArray();
                for (int id : menuItemIds) {
                    idsArray.put(id);
                }
                requestBody.put("product_ids", idsArray);
                requestBody.put("available", available);

                Log.d(TAG, "Bulk updating availability for " + menuItemIds.length + " items to " + (available ? "available" : "unavailable"));

                JSONObject response = makeRequest("PATCH", "/menu-items/bulk/availability", requestBody);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        Log.d(TAG, "Successfully bulk updated menu item availability");
                        callback.onSuccess(response);
                    } else {
                        String message = response != null ? response.optString("error", "Failed to bulk update availability") : "Failed to bulk update availability";
                        Log.e(TAG, "Failed to bulk update availability: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Bulk update availability error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    // Filter menu items (client-side filtering for search)
    public void getMenuItemsWithFilter(String search, String category, Boolean available, MenuItemsCallback callback) {
        executor.execute(() -> {
            try {
                StringBuilder urlBuilder = new StringBuilder("/menu-items");
                boolean hasParams = false;

                if (search != null && !search.trim().isEmpty()) {
                    urlBuilder.append(hasParams ? "&" : "?").append("search=").append(search);
                    hasParams = true;
                }
                if (category != null && !category.trim().isEmpty()) {
                    urlBuilder.append(hasParams ? "&" : "?").append("category=").append(category);
                    hasParams = true;
                }
                if (available != null) {
                    urlBuilder.append(hasParams ? "&" : "?").append("available=").append(available);
                }

                JSONObject response = makeRequest("GET", urlBuilder.toString(), null);
                mainHandler.post(() -> {
                    if (response != null && response.optBoolean("success", false)) {
                        try {
                            JSONArray menuItemsArray = response.getJSONArray("data");
                            Log.d(TAG, "Successfully fetched filtered menu items: " + menuItemsArray.length());
                            callback.onSuccess(menuItemsArray);
                        } catch (Exception e) {
                            Log.e(TAG, "Failed to parse filtered menu items response", e);
                            callback.onError("Failed to parse response: " + e.getMessage());
                        }
                    } else {
                        String message = response != null ? response.optString("error", "Failed to fetch filtered menu items") : "Failed to fetch filtered menu items";
                        Log.e(TAG, "Failed to get filtered menu items: " + message);
                        callback.onError(message);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get filtered menu items error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    // ApiService.java
    public void getAssignedOrders(OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders-assigned", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load assigned orders");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get assigned orders error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public interface EarningsCallback {
        void onSuccess(JSONObject data);
        void onError(String error);
    }

    public void getShipperEarnings(EarningsCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/shipper-earnings", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load earnings summary");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get shipper earnings error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getCompletedOrders(OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders-completed", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load completed orders");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get completed orders error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void markOrderAsCompleted(int orderId, SimpleCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject res = makeRequest("POST", "/orders/" + orderId + "/complete", null);
                mainHandler.post(() -> {
                    if (res != null && res.optBoolean("success", false)) {
                        callback.onSuccess();
                    } else {
                        callback.onError("Failed to mark order");
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError(e.getMessage()));
            }
        });
    }
    public void getOrderDetail(int orderId, OrderCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject response = makeRequest("GET", "/orders-detail/" + orderId, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to load order detail");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get order detail error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }



    public interface SimpleCallback {
        void onSuccess();
        void onError(String error);
    }


    public interface TasksCallback {
        void onSuccess(JSONArray tasks);
        void onError(String error);
    }

    public interface TaskActionCallback {
        void onSuccess(JSONObject result);
        void onError(String error);
    }

    public void getDailyTasks(TasksCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/tasks", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch daily tasks");
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateTaskCompletion(int taskId, boolean completed, TaskActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("completed", completed);

                JSONObject response = makeRequest("PUT", "/tasks/" + taskId + "/complete", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to update task completion");
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getAllOrders(OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch orders");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get all orders error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getActiveOrders(OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders/active", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch active orders");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get active orders error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    public void getOrdersByStatus(String status, OrdersCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/orders/status/" + status, null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch orders by status");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get orders by status error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }
    // ==================== INVENTORY METHODS ====================

    public interface InventoryCallback {
        void onSuccess(JSONArray inventoryItems);
        void onError(String error);
    }

    public interface InventoryActionCallback {
        void onSuccess(JSONObject result);
        void onError(String error);
    }

    public void getInventoryItems(InventoryCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/inventory", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch inventory items");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get inventory items error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getLowStockItems(InventoryCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/inventory/low-stock", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch low stock items");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get low stock items error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void getOutOfStockItems(InventoryCallback callback) {
        executor.execute(() -> {
            try {
                JSONArray response = makeArrayRequest("GET", "/inventory/out-of-stock", null);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to fetch out of stock items");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Get out of stock items error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }

    public void updateInventoryStock(int itemId, int quantity, InventoryActionCallback callback) {
        executor.execute(() -> {
            try {
                JSONObject requestBody = new JSONObject();
                requestBody.put("quantity", quantity);

                JSONObject response = makeRequest("PUT", "/inventory/" + itemId + "/stock", requestBody);
                mainHandler.post(() -> {
                    if (response != null) {
                        callback.onSuccess(response);
                    } else {
                        callback.onError("Failed to update inventory stock");
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Update inventory stock error", e);
                mainHandler.post(() -> callback.onError("Network error: " + e.getMessage()));
            }
        });
    }


}