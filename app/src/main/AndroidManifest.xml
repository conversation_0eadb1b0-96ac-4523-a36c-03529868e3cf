<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.CoffeeShop"
        tools:targetApi="31">

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBBLc8LHI0WPjyjNpt_o3W3wGZgAP4Yjjs" />

        <!-- Login Activity - Main Entry Point -->
        <activity
            android:name=".activities.LoginActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Register Activity -->
        <activity
            android:name=".activities.RegisterActivity"
            android:exported="true" />

        <!-- Cart Activity -->
        <activity
            android:name=".activities.CartActivity"
            android:exported="false" />

        <!-- Role-specific Dashboard Activities -->
        <activity
            android:name=".activities.CustomerDashboardActivity"
            android:exported="false" />
        <activity
            android:name=".activities.BaristaDashboardActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ShipperDashboardActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ManagerDashboardActivity"
            android:exported="false" />
        <activity
            android:name=".activities.CustomerSupportDashboardActivity"
            android:exported="false" />

        <!-- Product Details Activity -->
        <activity
            android:name=".activities.ProductDetailsActivity"
            android:exported="false" />

        <!-- Customer Screens -->
        <activity
            android:name=".activities.CheckoutActivity"
            android:exported="false" />
        <activity
            android:name=".activities.OrderTrackingActivity"
            android:exported="false" />
        <activity
            android:name=".activities.OrderHistoryActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ProfileActivity"
            android:exported="false" />

        <!-- Support Ticket Activities -->
        <activity
            android:name=".activities.SupportTicketListActivity"
            android:exported="true" />
        <activity
            android:name=".activities.SupportTicketDetailActivity"
            android:exported="true" />
        <activity
            android:name=".activities.OrderHistoryForTicketActivity"
            android:exported="true" />
        <activity
            android:name=".activities.RefundOrCreditActivity"
            android:exported="true" />
        <activity
            android:name=".activities.PaymentFailureActivity"
            android:exported="true" />
        <activity
            android:name=".activities.EscalateTicketActivity"
            android:exported="true" />

        <!-- Manager Account Management Screens -->
        <activity
            android:name=".activities.AccountManagementActivity"
            android:exported="false"
            android:parentActivityName=".activities.ManagerDashboardActivity" />
        <activity
            android:name=".activities.AddAccountActivity"
            android:exported="false"
            android:parentActivityName=".activities.AccountManagementActivity" />
        <activity
            android:name=".activities.EditAccountActivity" />

        <!-- Barista Screens -->
        <activity
            android:name=".activities.BaristaOrderQueueActivity"
            android:exported="false" />
        <activity
            android:name=".activities.BaristaRecipeGuideActivity"
            android:exported="false" />
        <activity
            android:name=".activities.RecipeDetailActivity"
            android:exported="false" />
        <activity
            android:name=".activities.BaristaInventoryActivity"
            android:exported="false" />
        <activity
            android:name=".activities.BaristaReadyOrdersActivity"
            android:exported="false" />
        <activity
            android:name=".activities.BaristaDailyTasksActivity"
            android:exported="false" />
        <activity
            android:name=".activities.BaristaQuickActionsActivity"
            android:exported="false" />

        <!-- Shipper Screens -->
        <activity
            android:name=".activities.ShipperViewAssignedOrdersActivity"
            android:exported="false" />
        <activity
<<<<<<< HEAD
            android:name=".activities.ShipperUpdateOrderStatusActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ShipperContactCustomerActivity"
=======
            android:name=".activities.ShipperCompletedOrdersActivity"
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
            android:exported="false" />
        <activity
            android:name=".activities.ShipperOrderDetailsActivity"
            android:exported="false" />
        <activity
<<<<<<< HEAD
            android:name=".activities.ShipperViewMapActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ShipperTrackOrderActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ShipperEarningsSummaryActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ShipperViewScheduleActivity"
            android:exported="false" />

=======
            android:name=".activities.ShipperEarningsSummaryActivity"
            android:exported="false" />
        <activity
            android:name=".activities.ShipperProfile"
            android:exported="false" />

        <!-- Manager Promotion Management Screens -->
        <activity
            android:name=".activities.PromotionManagementActivity"
            android:exported="false"
            android:parentActivityName=".activities.ManagerDashboardActivity" />

        <activity
            android:name=".activities.AddPromotionActivity"
            android:parentActivityName=".activities.PromotionManagementActivity"
            android:label="Add Promotion" />

        <activity
            android:name=".activities.EditPromotionActivity"
            android:parentActivityName=".activities.PromotionManagementActivity"
            android:label="Edit Promotion" />

        <!-- Manager Menu Item Management Screens -->
        <activity
            android:name=".activities.MenuItemManagementActivity"
            android:exported="false"
            android:parentActivityName=".activities.ManagerDashboardActivity" />

        <activity
            android:name=".activities.AddMenuItemActivity"
            android:parentActivityName=".activities.MenuItemManagementActivity"
            android:label="Add Menu Item" />

        <activity
            android:name=".activities.EditMenuItemActivity"
            android:parentActivityName=".activities.MenuItemManagementActivity"
            android:label="Edit Menu Item" />

>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
    </application>
</manifest>
