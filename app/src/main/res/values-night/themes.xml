<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base Night Theme for CoffeeShop -->
    <style name="Theme.CoffeeShop" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Brand colors -->
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryVariant">@color/primary</item>
        <item name="colorOnPrimary">@color/black</item>

        <!-- Accent and secondary -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/secondary</item>
        <item name="colorOnSecondary">@color/black</item>

        <!-- Background and surface -->
        <item name="android:colorBackground">@color/background_night</item>
        <item name="colorSurface">@color/surface_night</item>
        <item name="colorOnBackground">@color/text_primary_night</item>
        <item name="colorOnSurface">@color/text_primary_night</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/black</item>        <!-- AppBar/Header fix for dark mode -->
        <item name="colorPrimarySurface">#1E1E1E</item> <!-- Darker header background -->

        <!-- Card & input overrides -->
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="materialCardViewStyle">@style/Widget.CoffeeShop.Night.CardView</item>
        <item name="textInputStyle">@style/Widget.CoffeeShop.Night.TextInputLayout</item>

        <!-- Typography -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.CoffeeShop.Night.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.CoffeeShop.Night.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.CoffeeShop.Night.Headline3</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.CoffeeShop.Night.Subtitle1</item>
        <item name="textAppearanceBody1">@style/TextAppearance.CoffeeShop.Night.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.CoffeeShop.Night.Body2</item>
        <item name="textAppearanceCaption">@style/TextAppearance.CoffeeShop.Night.Caption</item>

        <!-- Shapes -->
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.CoffeeShop.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.CoffeeShop.MediumComponent</item>
    </style>

    <!-- Top App Bar (Toolbar) style for night mode -->
    <style name="Widget.CoffeeShop.Night.TopAppBar" parent="Widget.MaterialComponents.Toolbar.Primary">
        <item name="android:background">#1E1E1E</item>
        <item name="titleTextColor">@color/text_primary</item>
        <item name="navigationIconTint">@color/text_primary</item>
    </style>

    <!-- CardView Dark -->
    <style name="Widget.CoffeeShop.Night.CardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="contentPadding">16dp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
    </style>

    <!-- TextInputLayout Dark -->
    <style name="Widget.CoffeeShop.Night.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxBackgroundColor">@color/surface</item>
        <item name="boxStrokeColor">@color/primary_light</item>
        <item name="hintTextColor">@color/accent</item>
    </style>

    <!-- Typography -->
    <style name="TextAppearance.CoffeeShop.Night.Headline1" parent="TextAppearance.MaterialComponents.Headline1">
        <item name="android:textSize">28sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Night.Headline2" parent="TextAppearance.MaterialComponents.Headline2">
        <item name="android:textSize">24sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Night.Headline3" parent="TextAppearance.MaterialComponents.Headline3">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Night.Subtitle1" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Night.Body1" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Night.Body2" parent="TextAppearance.MaterialComponents.Body2">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="TextAppearance.CoffeeShop.Night.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>

</resources>
