<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="20dp">

                <ImageButton
                    android:id="@+id/btn_back"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_arrow_back"
                    android:contentDescription="Back" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Profile &amp; Settings"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="#333"
                    android:gravity="center" />

                <View
                    android:layout_width="40dp"
                    android:layout_height="40dp" />

            </LinearLayout>

            <!-- Profile Avatar Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <ImageView
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:src="@drawable/ic_person"
                        android:background="@drawable/circle_background"
                        android:padding="20dp"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Shipper Profile"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#333" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Manage your personal information"
                        android:textSize="14sp"
                        android:textColor="#666"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Personal Information Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Personal Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#333"
                        android:layout_marginBottom="16dp" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="Full Name"
                        app:startIconDrawable="@drawable/ic_person"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_full_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="Email Address"
                        app:startIconDrawable="@drawable/ic_email"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_email"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textEmailAddress" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="Phone Number"
                        app:startIconDrawable="@drawable/ic_phone"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_phone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_save_profile"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="Save Changes"
                        android:textSize="16sp"
                        android:textColor="@android:color/white"
                        android:background="@drawable/button_primary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Account Settings Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Account Settings"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#333"
                        android:layout_marginBottom="16dp" />

                    <Button
                        android:id="@+id/btn_change_password"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="Change Password"
                        android:textSize="16sp"
                        android:textColor="#8B4513"
                        android:background="@drawable/button_outline"
                        android:layout_marginBottom="12dp"
                        android:drawableStart="@drawable/ic_lock"
                        android:drawablePadding="12dp" />

                    <Button
                        android:id="@+id/btn_logout"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:text="Logout"
                        android:textSize="16sp"
                        android:textColor="@android:color/white"
                        android:background="@drawable/button_danger"
                        android:drawableStart="@drawable/ic_logout"
                        android:drawablePadding="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- App Info Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="App Information"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="#333"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Coffee Shop Management System"
                        android:textSize="16sp"
                        android:textColor="#666"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Version 1.0.0"
                        android:textSize="14sp"
                        android:textColor="#999" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/shipper_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
