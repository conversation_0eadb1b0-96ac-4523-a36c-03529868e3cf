<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f5f5f5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        android:orientation="vertical"
        android:clipToPadding="false"
        android:paddingBottom="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        android:background="@android:color/white"
        android:elevation="4dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_back"
            android:contentDescription="Back" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Order History"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333"
            android:gravity="center" />

        <View
            android:layout_width="40dp"
            android:layout_height="40dp" />

    </LinearLayout>

    <!-- Subtitle -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Orders from the last 3 months"
        android:textSize="14sp"
        android:textColor="#666"
        android:gravity="center"
        android:padding="12dp"
        android:background="@android:color/white" />

    <!-- Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Order History List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_order_history"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="16dp"
                android:clipToPadding="false" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layout_empty_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:src="@drawable/ic_history"
                    android:alpha="0.5"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tv_empty_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No orders found in the last 3 months"
                    android:textSize="16sp"
                    android:textColor="#666"
                    android:gravity="center"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Start browsing our menu to place your first order!"
                    android:textSize="14sp"
                    android:textColor="#999"
                    android:gravity="center" />

            </LinearLayout>

        </FrameLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/customer_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
