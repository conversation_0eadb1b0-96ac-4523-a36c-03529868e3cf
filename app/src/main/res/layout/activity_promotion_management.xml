<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activities.PromotionManagementActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Search Bar -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Search promotions..."
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Type Filter Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Filter by Type"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_types"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:singleSelection="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_all_types"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="All Types"
                    android:checked="true"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_percentage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Percentage"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_fixed_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Fixed Amount"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_buy_x_get_y"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Buy X Get Y"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

            </com.google.android.material.chip.ChipGroup>

            <!-- Status Filter Section -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Filter by Status"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.chip.ChipGroup
                android:id="@+id/chip_group_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:singleSelection="false">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_all_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="All Status"
                    android:checked="true"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_active"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Active"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_inactive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Inactive"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chip_expired"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Expired"
                    style="@style/Widget.MaterialComponents.Chip.Filter" />

            </com.google.android.material.chip.ChipGroup>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_add_promotion"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="Add Promotion"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_sort"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="Sort"
                    app:icon="@android:drawable/ic_menu_sort_by_size"
                    app:iconGravity="textStart"
                    app:strokeColor="@color/primary" />

            </LinearLayout>

            <!-- Promotions List -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_promotions"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:listitem="@layout/item_promotion" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layout_empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone">

                    <ImageView
                        android:layout_width="120dp"
                        android:layout_height="120dp"
                        android:alpha="0.5"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No promotions found"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Try adjusting your search or filters"
                        android:textSize="14sp"
                        android:alpha="0.7" />

                </LinearLayout>

            </FrameLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>