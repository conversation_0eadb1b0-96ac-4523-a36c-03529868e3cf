<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_step"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="@color/step_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Step Number -->
        <TextView
            android:id="@+id/tv_step_number"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/circle_background"
            android:backgroundTint="@color/primary_color"
            android:text="1"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginEnd="12dp" />

        <!-- Step Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Instruction -->
            <TextView
                android:id="@+id/tv_step_instruction"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Grind 18g of coffee beans to a fine consistency"
                android:textSize="14sp"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="4dp" />

            <!-- Duration (if available) -->
            <TextView
                android:id="@+id/tv_step_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⏱ 30s"
                android:textSize="12sp"
                android:textColor="@color/accent_color"
                android:textStyle="bold"
                android:layout_marginBottom="4dp"
                android:visibility="gone" />

            <!-- Tip (if available) -->
            <TextView
                android:id="@+id/tv_step_tip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="💡 Tip: Use a burr grinder for consistent results"
                android:textSize="12sp"
                android:textColor="@color/text_secondary"
                android:textStyle="italic"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
