<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Barista Dashboard"
            app:titleTextColor="@color/white">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_pending_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/circle_background"
                    android:text="0"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:padding="6dp"
                    android:minWidth="24dp"
                    android:gravity="center" />

            </LinearLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Status Filter Tabs -->
                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tab_status_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@color/surface"
                    app:tabGravity="fill"
                    app:tabMode="scrollable"
                    app:tabSelectedTextColor="@color/primary"
                    app:tabTextColor="@color/text_secondary"
                    app:tabIndicatorColor="@color/primary" />

                <!-- Orders Header -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_orders_title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Incoming Orders"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary" />

                    <TextView
                        android:id="@+id/tv_orders_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0 orders"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

                <!-- Orders RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_orders"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_barista_order" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layout_empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="64dp"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="☕"
                        android:textSize="48sp"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No orders yet"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Orders will appear here when customers place them"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center"
                        android:layout_marginBottom="16dp" />

                </LinearLayout>

                <!-- Quick Actions Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="24dp"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Quick Actions"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Daily Tasks Button -->
                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/card_daily_tasks"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        app:cardCornerRadius="8dp"
                        app:cardElevation="2dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="16dp"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                android:src="@drawable/ic_help"
                                android:layout_marginEnd="16dp"
                                app:tint="@color/primary" />

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="Daily Tasks"
                                android:textSize="16sp"
                                android:textColor="@color/text_primary" />

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:src="@drawable/ic_arrow_forward"
                                app:tint="@color/text_secondary" />

                        </LinearLayout>

                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/barista_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
