<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5ECE2">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Earning Summary"
            app:titleTextColor="@color/white" />
    </com.google.android.material.appbar.AppBarLayout>
    <!-- <PERSON><PERSON><PERSON> dung cuộn -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal">

            <TextView
                android:text="Earnings Summary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="#5D4037"
                android:layout_marginBottom="16dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="4dp"
                android:layout_marginBottom="16dp"
                android:backgroundTint="@android:color/white">

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tv_completed_orders"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Completed Orders: 0"
                        android:textSize="16sp"
                        android:textColor="#6D4C41"
                        android:paddingBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_total_earnings"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Total Earnings: $0.00"
                        android:textSize="16sp"
                        android:textColor="#6D4C41"
                        android:paddingBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_average_per_order"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Average per Order: $0.00"
                        android:textSize="16sp"
                        android:textColor="#6D4C41" />

                </LinearLayout>
            </androidx.cardview.widget.CardView>
            <!-- New Button -->
            <Button
                android:id="@+id/btn_view_completed_orders"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="View Completed Orders"
                android:backgroundTint="#795548"
                android:textColor="@android:color/white"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="16dp" />
        </LinearLayout>
    </ScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:menu="@menu/shipper_bottom_menu"/>

</LinearLayout>
