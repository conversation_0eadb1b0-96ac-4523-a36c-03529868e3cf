<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Image and Basic Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_menu_item_image"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginEnd="16dp"
                android:background="@android:color/transparent"
                android:scaleType="centerCrop"
                android:src="@android:drawable/ic_menu_gallery" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_menu_item_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Menu Item Name"
                    android:textAppearance="@android:style/TextAppearance.Material.Headline"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_menu_item_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Description"
                    android:textAppearance="@android:style/TextAppearance.Material.Body1"
                    android:textColor="@android:color/darker_gray"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_menu_item_price"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="$0.00"
                        android:textAppearance="@android:style/TextAppearance.Material.Headline"
                        android:textColor="@android:color/holo_green_dark"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_menu_item_category"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="Category"
                        android:textAppearance="@android:style/TextAppearance.Material.Caption"
                        android:textColor="@android:color/darker_gray" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Status Chips -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Category"
                style="@style/Widget.MaterialComponents.Chip.Action" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_availability"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="Available"
                style="@style/Widget.MaterialComponents.Chip.Action" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Active"
                style="@style/Widget.MaterialComponents.Chip.Action" />

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_menu_item"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="Edit"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_availability"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="Toggle"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="Status"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete_menu_item"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="Delete"
                android:backgroundTint="@android:color/holo_red_light"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>