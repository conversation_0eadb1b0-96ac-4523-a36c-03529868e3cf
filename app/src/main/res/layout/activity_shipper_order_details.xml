<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5ECE2">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Order Details"
            app:titleTextColor="@android:color/white" />
    </com.google.android.material.appbar.AppBarLayout>

    <!-- <PERSON><PERSON><PERSON> dung cuộn -->
    <ScrollView
        android:id="@+id/scroll_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/bottom_navigation"
        android:layout_below="@id/appbar"
        android:padding="16dp">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:backgroundTint="@android:color/white"
            android:elevation="4dp"
            app:cardCornerRadius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tv_order_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="8dp"
                    android:text="Order #12345"
                    android:textColor="#5D4037"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginBottom="8dp"
                    android:background="#D7CCC8" />

                <TextView
                    android:id="@+id/tv_customer_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="4dp"
                    android:text="Customer: John Doe"
                    android:textColor="#6D4C41"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_delivery_address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="4dp"
                    android:text="Address: 123 Main St"
                    android:textColor="#6D4C41"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_total_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="4dp"
                    android:text="Total: $100.00"
                    android:textColor="#6D4C41"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_order_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="4dp"
                    android:text="Status: Pending"
                    android:textColor="#BF360C"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_created_at"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="4dp"
                    android:text="Created At: 2025-07-16"
                    android:textColor="#6D4C41"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/tv_instructions"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingBottom="16dp"
                    android:text="Instructions: Leave at the door"
                    android:textColor="#6D4C41"
                    android:textSize="16sp" />

                <Button
                    android:id="@+id/btn_view_map"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:backgroundTint="#795548"
                    android:text="View Map"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btn_contact_customer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:backgroundTint="#8D6E63"
                    android:text="Contact Customer"
                    android:textColor="@android:color/white" />

                <Button
                    android:id="@+id/btn_update_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:backgroundTint="#4E342E"
                    android:text="Mark as Completed"
                    android:textColor="@android:color/white" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </ScrollView>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        app:menu="@menu/shipper_bottom_menu"/>
</RelativeLayout>
