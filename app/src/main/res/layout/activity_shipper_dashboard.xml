<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".activities.ShipperDashboardActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Shipper Dashboard"
            app:titleTextColor="@color/white" />
    </com.google.android.material.appbar.AppBarLayout>

    <!-- <PERSON><PERSON><PERSON> dung chính -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="80dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp"
                android:gravity="center_horizontal">

                <!-- Ảnh banner -->
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_delivery_truck"
                    android:contentDescription="@string/app_name"
                    android:layout_marginBottom="24dp"
                    android:adjustViewBounds="true"
                    android:scaleType="fitCenter"
                    android:maxWidth="300dp"
                    android:maxHeight="300dp" />


                <!-- Lời chào -->
                <TextView
                    android:id="@+id/tv_welcome"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Welcome back, Shipper!"
                    android:textAppearance="?attr/textAppearanceHeadline5"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="8dp" />

                <!-- Câu quote truyền cảm hứng -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="“Delivering smiles, one order at a time.”"
                    android:textAppearance="?attr/textAppearanceBody1"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="24dp"
                    android:gravity="center" />

                <!-- Progress Bar giả lập -->
                <com.google.android.material.progressindicator.LinearProgressIndicator
                    android:id="@+id/progress_indicator"
                    android:layout_width="200dp"
                    android:layout_height="wrap_content"
                    android:progress="60"
                    android:max="100"
                    android:layout_marginBottom="8dp"
                    app:indicatorColor="@color/primary"
                    app:trackColor="@color/surface" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Today’s progress: 60%"
                    android:textAppearance="?attr/textAppearanceBody2"
                    android:textColor="@color/text_secondary"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:menu="@menu/shipper_bottom_menu"/>


</androidx.coordinatorlayout.widget.CoordinatorLayout>
