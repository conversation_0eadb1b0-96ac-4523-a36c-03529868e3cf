<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header with Code and Status -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_promotion_code"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SUMMER20"
                android:textSize="18sp"
                android:textStyle="bold" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_promotion_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Active"
                android:textSize="12sp"
                style="@style/Widget.MaterialComponents.Chip.Action" />

        </LinearLayout>

        <!-- Promotion Name -->
        <TextView
            android:id="@+id/tv_promotion_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Summer Sale"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <!-- Type and Value -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_promotion_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Percentage"
                android:textSize="12sp"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Chip.Action" />

            <TextView
                android:id="@+id/tv_promotion_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="20% OFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="end" />

        </LinearLayout>

        <!-- Date Range -->
        <TextView
            android:id="@+id/tv_date_range"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2024-06-01 - 2024-08-31"
            android:textSize="14sp"
            android:drawablePadding="8dp"
            android:layout_marginBottom="4dp" />

        <!-- Applicable Products -->
        <TextView
            android:id="@+id/tv_applicable_products"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="All Products"
            android:textSize="14sp"
            android:drawablePadding="8dp"
            android:layout_marginBottom="4dp" />

        <!-- Usage Count -->
        <TextView
            android:id="@+id/tv_usage_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="15/100 used"
            android:textSize="14sp"
            android:drawablePadding="8dp"
            android:layout_marginBottom="16dp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_edit_promotion"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="Edit"
                android:textSize="12sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="Deactivate"
                android:textSize="12sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete_promotion"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="Delete"
                android:textSize="12sp"
                android:textColor="@android:color/white" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>