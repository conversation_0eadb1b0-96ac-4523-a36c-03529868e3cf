<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/Widget.CoffeeShop.ListItem"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">        <ImageView
            android:id="@+id/iv_product_image"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:contentDescription="Product image"
            android:scaleType="centerCrop"
            android:background="@color/surface"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Gradient overlay for better text readability -->
        <View
            android:layout_width="100dp"
            android:layout_height="30dp"
            android:background="@drawable/gradient_overlay"
            app:layout_constraintBottom_toBottomOf="@+id/iv_product_image"
            app:layout_constraintStart_toStartOf="@+id/iv_product_image" />

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:text="Cappuccino"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            android:textStyle="bold"
            android:textSize="18sp"
            app:layout_constraintEnd_toStartOf="@+id/tv_product_price"
            app:layout_constraintStart_toEndOf="@+id/iv_product_image"
            app:layout_constraintTop_toTopOf="@+id/iv_product_image" />

        <TextView
            android:id="@+id/tv_product_category"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:text="Hot Coffee"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="@color/secondary"
            android:textStyle="italic"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@+id/tv_product_price"
            app:layout_constraintStart_toStartOf="@+id/tv_product_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_name" />

        <TextView
            android:id="@+id/tv_product_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="8dp"
            android:text="Espresso with steamed milk and foam"
            android:textAppearance="?attr/textAppearanceBody2"
            android:textColor="@color/text_secondary"
            android:maxLines="2"
            android:ellipsize="end"
            android:textSize="14sp"
            app:layout_constraintEnd_toStartOf="@+id/tv_product_price"
            app:layout_constraintStart_toStartOf="@+id/tv_product_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_category" />

        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="$4.00"
            android:textAppearance="?attr/textAppearanceSubtitle1"
            android:textColor="@color/primary"
            android:textStyle="bold"
            android:textSize="20sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_product_image" />

        <TextView
            android:id="@+id/tv_stock_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="In Stock"
            android:textAppearance="?attr/textAppearanceCaption"
            android:textColor="@color/success"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="@+id/tv_product_price"
            app:layout_constraintTop_toBottomOf="@+id/tv_product_price" />        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_add_to_cart"
            style="@style/Widget.MaterialComponents.Button"
            android:layout_width="0dp"
            android:layout_height="44dp"
            android:layout_marginTop="12dp"
            android:text="Add to Cart"
            android:textSize="14sp"
            android:textStyle="bold"
            android:backgroundTint="@color/primary"
            android:elevation="4dp"
            app:icon="@android:drawable/ic_input_add"
            app:iconGravity="textStart"
            app:iconTint="@color/white"
            app:cornerRadius="22dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="@+id/tv_product_name"
            app:layout_constraintTop_toBottomOf="@+id/iv_product_image" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
