<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:elevation="4dp"
            app:title="Order Queue"
            app:titleTextColor="@color/white">

            <TextView
                android:id="@+id/tv_queue_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="16dp"
                android:background="@drawable/circle_background"
                android:text="0"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                android:padding="6dp"
                android:minWidth="24dp"
                android:gravity="center" />

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="80dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Filter Chips -->
                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_filter"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_all"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="All Orders"
                        android:checked="true"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_pending"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Pending"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_priority"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Priority"
                        style="@style/Widget.MaterialComponents.Chip.Choice" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Orders List -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_orders"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_barista_order" />

                <!-- Empty State -->
                <LinearLayout
                    android:id="@+id/layout_empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="64dp"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="📋"
                        android:textSize="48sp"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="No orders in queue"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="New orders will appear here"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/barista_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
