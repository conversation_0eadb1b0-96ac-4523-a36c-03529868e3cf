<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header with Product Image -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="300dp">

            <ImageView
                android:id="@+id/iv_product_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/surface"
                android:src="@mipmap/ic_launcher" />

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_margin="16dp"
                android:background="@drawable/circle_background"
                android:padding="12dp"
                android:src="@android:drawable/ic_menu_revert"
                android:backgroundTint="@color/white"
                android:clickable="true"
                android:focusable="true" />

        </RelativeLayout>

        <!-- Product Information -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Product Name and Price -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="12dp">

                <TextView
                    android:id="@+id/tv_product_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Espresso"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/tv_product_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$3.50"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary" />

            </LinearLayout>

            <!-- Rating -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4.5"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/warning" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="★"
                    android:textSize="16sp"
                    android:textColor="@color/warning"
                    android:layout_marginStart="4dp" />

                <TextView
                    android:id="@+id/tv_review_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(23 reviews)"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <!-- Description -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Description"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_product_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Rich and bold espresso shot, perfect for coffee lovers who appreciate strong flavor."
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:lineHeight="20dp"
                android:layout_marginBottom="24dp" />

            <!-- Add to Cart Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_add_to_cart"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Add to Cart"
                android:textSize="16sp"
                android:textStyle="bold"
                app:backgroundTint="@color/primary"
                app:cornerRadius="8dp"
                android:layout_marginBottom="24dp" />

            <!-- Reviews Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Customer Reviews"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="12dp" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_reviews"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
