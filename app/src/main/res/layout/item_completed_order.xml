<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_margin="8dp"
    android:elevation="4dp"
    android:backgroundTint="@android:color/white"
    app:cardCornerRadius="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_order_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Order #12345"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#5D4037"/>

        <TextView
            android:id="@+id/tv_customer_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Customer: <PERSON>"
            android:textColor="#6D4C41"
            android:textSize="14sp"
            android:paddingTop="4dp"/>

        <TextView
            android:id="@+id/tv_total_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Total: $100.00"
            android:textColor="#6D4C41"
            android:textSize="14sp"
            android:paddingTop="4dp"/>

        <TextView
            android:id="@+id/tv_created_at"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Completed At: 2025-07-16"
            android:textColor="#6D4C41"
            android:textSize="14sp"
            android:paddingTop="4dp"/>

    </LinearLayout>
</androidx.cardview.widget.CardView>
