<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".activities.ShipperViewAssignedOrdersActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:title="Assigned Orders"
            android:titleTextColor="@android:color/white"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"/>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingBottom="80dp">

            <TextView
                android:id="@+id/tv_assigned_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Your Assigned Orders"
                android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                android:textColor="@color/primary"
                android:gravity="center"
                android:padding="12dp"
                android:background="@color/surface"
                android:elevation="2dp"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:padding="8dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    tools:listitem="@layout/item_assigned_order"/>

                <TextView
                    android:id="@+id/tv_empty"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No assigned orders"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:layout_centerInParent="true"
                    android:visibility="gone"/>

                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_centerInParent="true"
                    android:visibility="gone"/>

            </RelativeLayout>

        </LinearLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@color/surface"
        app:itemIconTint="@color/primary"
        app:itemTextColor="@color/primary"
        app:menu="@menu/shipper_bottom_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
