<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    android:padding="24dp">

    <TextView
        android:id="@+id/tv_welcome"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Welcome to Manager Dashboard!"
        android:textAlignment="center"
        android:textAppearance="?attr/textAppearanceHeadline2"
        android:textColor="@color/primary"
        app:layout_constraintBottom_toTopOf="@+id/btn_logout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.143" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_logout"
        style="@style/Widget.CoffeeShop.Button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:text="Logout"
        android:backgroundTint="@color/error"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

<<<<<<< HEAD
=======

>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_account_management"
        style="@style/Widget.CoffeeShop.Button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Account Management"
<<<<<<< HEAD
        app:layout_constraintTop_toBottomOf="@id/tv_welcome"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_logout" />
    
=======
        app:layout_constraintBottom_toTopOf="@id/btn_logout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_welcome"
        app:layout_constraintVertical_bias="0.342" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_menu_item_management"
        style="@style/Widget.CoffeeShop.Button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Menu Item Management"
        app:layout_constraintBottom_toTopOf="@id/btn_logout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_welcome"
        app:layout_constraintVertical_bias="0.053" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_promotion_management"
        style="@style/Widget.CoffeeShop.Button"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Promotion Management"
        app:layout_constraintBottom_toTopOf="@id/btn_logout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_welcome"
        app:layout_constraintVertical_bias="0.602" />
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
</androidx.constraintlayout.widget.ConstraintLayout>
