<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:title="Menu Management" />

    <!-- Search and Filters Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@android:color/white">

        <!-- Search Bar -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Search menu items..."
            app:startIconDrawable="@android:drawable/ic_menu_search"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_search"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Category Filter -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"
            android:text="Category"
            android:textAppearance="@android:style/TextAppearance.Material.Subhead"
            android:textStyle="bold" />

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_categories"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all_categories"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="All"
                android:checked="true"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_hot_coffee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Hot Coffee"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_cold_coffee"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Cold Coffee"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_tea"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tea"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_snacks"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Snacks"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_desserts"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Desserts"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

        </com.google.android.material.chip.ChipGroup>

        <!-- Status Filter -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="8dp"
            android:text="Status"
            android:textAppearance="@android:style/TextAppearance.Material.Subhead"
            android:textStyle="bold" />

        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chip_group_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="All"
                android:checked="true"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_available"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Available"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_unavailable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Unavailable"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_active"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Active"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_inactive"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Inactive"
                style="@style/Widget.MaterialComponents.Chip.Filter" />

        </com.google.android.material.chip.ChipGroup>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_add_menu_item"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Add Item"
                app:icon="@android:drawable/ic_input_add" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_sort"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Sort"
                app:icon="@android:drawable/ic_menu_sort_alphabetically"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

        </LinearLayout>

    </LinearLayout>

    <!-- Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- RecyclerView for Menu Items -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_menu_items"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty_state"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            android:padding="32dp">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="24dp"
                android:src="@drawable/ic_menu_item_placeholder"
                android:alpha="0.5" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No menu items found"
                android:textAppearance="@android:style/TextAppearance.Material.Headline"
                android:textColor="@android:color/black"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Add your first menu item to get started"
                android:textAppearance="@android:style/TextAppearance.Material.Body1"
                android:textColor="@android:color/darker_gray"
                android:textAlignment="center" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
