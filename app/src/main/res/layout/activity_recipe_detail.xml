<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_color">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/primary_color"
                app:title="Recipe Details"
                app:titleTextColor="@android:color/white" />

        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Recipe Info Card -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:id="@+id/tv_recipe_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Espresso"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_recipe_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Classic Italian espresso with perfect crema"
                        android:textSize="16sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="⏱"
                                android:textSize="18sp"
                                android:layout_marginEnd="8dp" />

                            <TextView
                                android:id="@+id/tv_preparation_time"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="3 min"
                                android:textSize="14sp"
                                android:textColor="@color/text_secondary" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📊"
                                android:textSize="18sp"
                                android:layout_marginEnd="8dp" />

                            <TextView
                                android:id="@+id/tv_difficulty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Medium"
                                android:textSize="14sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Timer Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_timer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/accent_color">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp"
                    android:gravity="center">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Timer"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tv_timer_display"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="03:00"
                        android:textSize="32sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/white"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_start_timer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Start"
                            android:layout_marginEnd="8dp"
                            app:backgroundTint="@color/primary_color" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_stop_timer"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Stop"
                            android:visibility="gone"
                            app:backgroundTint="@android:color/holo_red_dark" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Ingredients Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_ingredients"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Ingredients"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/tv_ingredients"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• 18g freshly ground coffee\n• 30ml hot water"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Steps -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Instructions"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_steps"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Back Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_back_to_guide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Back to Recipe Guide"
                android:layout_marginBottom="20dp"
                app:backgroundTint="@color/primary_color" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
