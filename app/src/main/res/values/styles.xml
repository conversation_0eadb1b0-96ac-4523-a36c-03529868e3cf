<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Common button styles -->
    <style name="Widget.CoffeeShop.Button" parent="Widget.MaterialComponents.Button">
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <style name="Widget.CoffeeShop.Button.OutlinedButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="strokeColor">@color/primary</item>
        <item name="strokeWidth">1dp</item>
    </style>

    <style name="Widget.CoffeeShop.Button.TextButton" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="rippleColor">@color/primary_light</item>
    </style>

    <!-- Common card styles -->
    <style name="Widget.CoffeeShop.Card" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="contentPadding">16dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>

    <!-- Text input styles -->
    <style name="Widget.CoffeeShop.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusBottomEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/secondary</item>
    </style>

    <!-- Common list item styles -->
    <style name="Widget.CoffeeShop.ListItem" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="contentPadding">12dp</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
    </style>
</resources>

