const express = require('express');
const router = express.Router();
const db = require('../db/init');

// Get all promotions with optional filters
router.get('/', (req, res) => {
    const { active, search, discount_type } = req.query;
    
    let query = 'SELECT * FROM promotions WHERE 1=1';
    let params = [];
    
    if (active !== undefined) {
        query += ' AND is_active = ?';
        params.push(active === 'true' ? 1 : 0);
    }
    
    if (search) {
        query += ' AND (title LIKE ? OR description LIKE ? OR promo_code LIKE ?)';
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
    }
    
    if (discount_type) {
        query += ' AND discount_type = ?';
        params.push(discount_type);
    }
    
    query += ' ORDER BY created_at DESC';
    
    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching promotions:', err);
            return res.status(500).json({ 
                success: false, 
                message: 'Error fetching promotions',
                error: err.message 
            });
        }
        
        res.json({
            success: true,
            data: rows
        });
    });
});

// Get promotion by ID
router.get('/:id', (req, res) => {
    const { id } = req.params;
    
    db.get('SELECT * FROM promotions WHERE promotion_id = ?', [id], (err, row) => {
        if (err) {
            console.error('Error fetching promotion:', err);
            return res.status(500).json({ 
                success: false, 
                message: 'Error fetching promotion',
                error: err.message 
            });
        }
        
        if (!row) {
            return res.status(404).json({ 
                success: false, 
                message: 'Promotion not found' 
            });
        }
        
        res.json({
            success: true,
            data: row
        });
    });
});

// Validate promo code
router.get('/validate/:promoCode', (req, res) => {
    const { promoCode } = req.params;
    const { orderAmount = 0 } = req.query;
    
    const query = `
        SELECT * FROM promotions 
        WHERE promo_code = ? 
        AND is_active = 1 
        AND start_date <= date('now') 
        AND end_date >= date('now')
        AND (usage_limit IS NULL OR current_usage < usage_limit)
        AND minimum_order_amount <= ?
    `;
    
    db.get(query, [promoCode, orderAmount], (err, row) => {
        if (err) {
            console.error('Error validating promo code:', err);
            return res.status(500).json({ 
                success: false, 
                message: 'Error validating promo code',
                error: err.message 
            });
        }
        
        if (!row) {
            return res.status(404).json({ 
                success: false, 
                message: 'Invalid or expired promo code' 
            });
        }
        
        // Calculate discount amount
        let discountAmount = 0;
        if (row.discount_type === 'percentage') {
            discountAmount = (orderAmount * row.discount_value) / 100;
        } else {
            discountAmount = row.discount_value;
        }
        
        res.json({
            success: true,
            data: {
                ...row,
                discount_amount: Math.min(discountAmount, orderAmount)
            }
        });
    });
});

// Create new promotion
router.post('/', (req, res) => {
    const {
        title,
        description,
        discount_type,
        discount_value,
        minimum_order_amount = 0,
        start_date,
        end_date,
        is_active = 1,
        usage_limit,
        applicable_products,
        promo_code
    } = req.body;
    
    // Validation
    if (!title || !discount_type || !discount_value || !start_date || !end_date) {
        return res.status(400).json({ 
            success: false, 
            message: 'Missing required fields: title, discount_type, discount_value, start_date, end_date' 
        });
    }
    
    if (!['percentage', 'fixed_amount', 'free_shipping'].includes(discount_type)) {
        return res.status(400).json({ 
            success: false, 
            message: 'discount_type must be either "percentage", "fixed_amount", or "free_shipping"' 
        });
    }
    
    if (discount_value <= 0) {
        return res.status(400).json({ 
            success: false, 
            message: 'discount_value must be greater than 0' 
        });
    }
    
    // Check if promo code already exists
    if (promo_code) {
        db.get('SELECT promotion_id FROM promotions WHERE promo_code = ?', [promo_code], (err, existing) => {
            if (err) {
                console.error('Error checking promo code:', err);
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error validating promo code',
                    error: err.message 
                });
            }
            
            if (existing) {
                return res.status(400).json({ 
                    success: false, 
                    message: 'Promo code already exists' 
                });
            }
            
            createPromotion();
        });
    } else {
        createPromotion();
    }
    
    function createPromotion() {
        const query = `
            INSERT INTO promotions (
                title, description, discount_type, discount_value, minimum_order_amount,
                start_date, end_date, is_active, usage_limit, applicable_products, promo_code
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        
        const params = [
            title, description, discount_type, discount_value, minimum_order_amount,
            start_date, end_date, is_active, usage_limit, applicable_products, promo_code
        ];
        
        db.run(query, params, function(err) {
            if (err) {
                console.error('Error creating promotion:', err);
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error creating promotion',
                    error: err.message 
                });
            }
            
            // Fetch the created promotion
            db.get('SELECT * FROM promotions WHERE promotion_id = ?', [this.lastID], (err, row) => {
                if (err) {
                    console.error('Error fetching created promotion:', err);
                    return res.status(500).json({ 
                        success: false, 
                        message: 'Promotion created but error fetching details',
                        error: err.message 
                    });
                }
                
                res.status(201).json({
                    success: true,
                    message: 'Promotion created successfully',
                    data: row
                });
            });
        });
    }
});

// Update promotion
router.put('/:id', (req, res) => {
    const { id } = req.params;
    const {
        title,
        description,
        discount_type,
        discount_value,
        minimum_order_amount,
        start_date,
        end_date,
        is_active,
        usage_limit,
        applicable_products,
        promo_code
    } = req.body;
    
    // Check if promotion exists
    db.get('SELECT * FROM promotions WHERE promotion_id = ?', [id], (err, existing) => {
        if (err) {
            console.error('Error checking promotion:', err);
            return res.status(500).json({ 
                success: false, 
                message: 'Error checking promotion',
                error: err.message 
            });
        }
        
        if (!existing) {
            return res.status(404).json({ 
                success: false, 
                message: 'Promotion not found' 
            });
        }
        
        // Validation
        if (discount_type && !['percentage', 'fixed_amount', 'free_shipping'].includes(discount_type)) {
            return res.status(400).json({ 
                success: false, 
                message: 'discount_type must be either "percentage", "fixed_amount", or "free_shipping"' 
            });
        }
        
        if (discount_value !== undefined && discount_value <= 0) {
            return res.status(400).json({ 
                success: false, 
                message: 'discount_value must be greater than 0' 
            });
        }
        
        // Check if promo code already exists (for other promotions)
        if (promo_code && promo_code !== existing.promo_code) {
            db.get('SELECT promotion_id FROM promotions WHERE promo_code = ? AND promotion_id != ?', [promo_code, id], (err, duplicate) => {
                if (err) {
                    console.error('Error checking promo code:', err);
                    return res.status(500).json({ 
                        success: false, 
                        message: 'Error validating promo code',
                        error: err.message 
                    });
                }
                
                if (duplicate) {
                    return res.status(400).json({ 
                        success: false, 
                        message: 'Promo code already exists' 
                    });
                }
                
                updatePromotion();
            });
        } else {
            updatePromotion();
        }
        
        function updatePromotion() {
            const updates = [];
            const params = [];
            
            if (title !== undefined) { updates.push('title = ?'); params.push(title); }
            if (description !== undefined) { updates.push('description = ?'); params.push(description); }
            if (discount_type !== undefined) { updates.push('discount_type = ?'); params.push(discount_type); }
            if (discount_value !== undefined) { updates.push('discount_value = ?'); params.push(discount_value); }
            if (minimum_order_amount !== undefined) { updates.push('minimum_order_amount = ?'); params.push(minimum_order_amount); }
            if (start_date !== undefined) { updates.push('start_date = ?'); params.push(start_date); }
            if (end_date !== undefined) { updates.push('end_date = ?'); params.push(end_date); }
            if (is_active !== undefined) { updates.push('is_active = ?'); params.push(is_active); }
            if (usage_limit !== undefined) { updates.push('usage_limit = ?'); params.push(usage_limit); }
            if (applicable_products !== undefined) { updates.push('applicable_products = ?'); params.push(applicable_products); }
            if (promo_code !== undefined) { updates.push('promo_code = ?'); params.push(promo_code); }
            
            updates.push('updated_at = CURRENT_TIMESTAMP');
            params.push(id);
            
            const query = `UPDATE promotions SET ${updates.join(', ')} WHERE promotion_id = ?`;
            
            db.run(query, params, function(err) {
                if (err) {
                    console.error('Error updating promotion:', err);
                    return res.status(500).json({ 
                        success: false, 
                        message: 'Error updating promotion',
                        error: err.message 
                    });
                }
                
                // Fetch updated promotion
                db.get('SELECT * FROM promotions WHERE promotion_id = ?', [id], (err, row) => {
                    if (err) {
                        console.error('Error fetching updated promotion:', err);
                        return res.status(500).json({ 
                            success: false, 
                            message: 'Promotion updated but error fetching details',
                            error: err.message 
                        });
                    }
                    
                    res.json({
                        success: true,
                        message: 'Promotion updated successfully',
                        data: row
                    });
                });
            });
        }
    });
});

// Toggle promotion status
router.patch('/:id/status', (req, res) => {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
        return res.status(400).json({ 
            success: false, 
            message: 'is_active field is required' 
        });
    }
    
    // Convert boolean or integer to proper integer value
    const activeValue = (is_active === true || is_active === 1 || is_active === '1') ? 1 : 0;
    
    console.log(`Toggling promotion ${id} status to ${activeValue}`);
    
    db.run(
        'UPDATE promotions SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE promotion_id = ?',
        [activeValue, id],
        function(err) {
            if (err) {
                console.error('Error updating promotion status:', err);
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error updating promotion status',
                    error: err.message 
                });
            }
            
            if (this.changes === 0) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Promotion not found' 
                });
            }
            
            res.json({
                success: true,
                message: `Promotion ${activeValue ? 'activated' : 'deactivated'} successfully`
            });
        }
    );
});

// Delete promotion
router.delete('/:id', (req, res) => {
    const { id } = req.params;
    
    db.run('DELETE FROM promotions WHERE promotion_id = ?', [id], function(err) {
        if (err) {
            console.error('Error deleting promotion:', err);
            return res.status(500).json({ 
                success: false, 
                message: 'Error deleting promotion',
                error: err.message 
            });
        }
        
        if (this.changes === 0) {
            return res.status(404).json({ 
                success: false, 
                message: 'Promotion not found' 
            });
        }
        
        res.json({
            success: true,
            message: 'Promotion deleted successfully'
        });
    });
});

// Update promotion usage (when a promo code is used)
router.patch('/:id/use', (req, res) => {
    const { id } = req.params;
    
    db.run(
        'UPDATE promotions SET current_usage = current_usage + 1 WHERE promotion_id = ?',
        [id],
        function(err) {
            if (err) {
                console.error('Error updating promotion usage:', err);
                return res.status(500).json({ 
                    success: false, 
                    message: 'Error updating promotion usage',
                    error: err.message 
                });
            }
            
            if (this.changes === 0) {
                return res.status(404).json({ 
                    success: false, 
                    message: 'Promotion not found' 
                });
            }
            
            res.json({
                success: true,
                message: 'Promotion usage updated successfully'
            });
        }
    );
});

module.exports = router;
