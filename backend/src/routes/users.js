const express = require('express');
const router = express.Router();
const db = require('../db/init');

// Get all users
router.get('/', (req, res) => {
    const { search, role, is_active } = req.query;
    
    let query = 'SELECT user_id, full_name, email, role, is_active, created_at FROM users WHERE 1=1';
    let params = [];
    
    if (search) {
        query += ' AND (full_name LIKE ? OR email LIKE ?)';
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm);
    }
    
    if (role) {
        query += ' AND role = ?';
        params.push(role);
    }
    
    if (is_active !== undefined) {
        query += ' AND is_active = ?';
        params.push(is_active === 'true' ? 1 : 0);
    }
    
    query += ' ORDER BY created_at DESC';
    
    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching users:', err);
            return res.status(500).json({ error: 'Failed to fetch users' });
        }
        res.json(rows);
    });
});

// Get user by ID
router.get('/:id', (req, res) => {
    const { id } = req.params;
    
    db.get('SELECT user_id, full_name, email, role, is_active, created_at FROM users WHERE user_id = ?', [id], (err, row) => {
        if (err) {
            console.error('Error fetching user:', err);
            return res.status(500).json({ error: 'Failed to fetch user' });
        }
        
        if (!row) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        res.json(row);
    });
});

// Create new user
router.post('/', (req, res) => {
    const { full_name, email, password, role = 'customer', phone, is_active = true } = req.body;
    
    if (!full_name || !email || !password) {
        return res.status(400).json({ error: 'Full name, email, and password are required' });
    }
    
    // Check if user already exists
    db.get('SELECT user_id FROM users WHERE email = ?', [email], (err, existingUser) => {
        if (err) {
            console.error('Error checking existing user:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        
        if (existingUser) {
            return res.status(409).json({ error: 'User with this email already exists' });
        }
        
        // Insert new user
        const stmt = db.prepare('INSERT INTO users (full_name, email, password, role, phone, is_active) VALUES (?, ?, ?, ?, ?, ?)');
        stmt.run([full_name, email, password, role, phone, is_active ? 1 : 0], function(err) {
            if (err) {
                console.error('Error creating user:', err);
                return res.status(500).json({ error: 'Failed to create user' });
            }
            
            res.status(201).json({
                user_id: this.lastID,
                full_name,
                email,
                role,
                is_active,
                message: 'User created successfully'
            });
        });
        stmt.finalize();
    });
});

// Update user
router.put('/:id', (req, res) => {
    const { id } = req.params;
    const { full_name, email, role, phone, is_active } = req.body;
    
    if (!full_name || !email) {
        return res.status(400).json({ error: 'Full name and email are required' });
    }
    
    const stmt = db.prepare('UPDATE users SET full_name = ?, email = ?, role = ?, phone = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?');
    stmt.run([full_name, email, role, phone, is_active ? 1 : 0, id], function(err) {
        if (err) {
            console.error('Error updating user:', err);
            return res.status(500).json({ error: 'Failed to update user' });
        }
        
        if (this.changes === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        res.json({ message: 'User updated successfully' });
    });
    stmt.finalize();
});

// Delete user
router.delete('/:id', (req, res) => {
    const { id } = req.params;
    
    const stmt = db.prepare('DELETE FROM users WHERE user_id = ?');
    stmt.run([id], function(err) {
        if (err) {
            console.error('Error deleting user:', err);
            return res.status(500).json({ error: 'Failed to delete user' });
        }
        
        if (this.changes === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        res.json({ message: 'User deleted successfully' });
    });
    stmt.finalize();
});

module.exports = router;