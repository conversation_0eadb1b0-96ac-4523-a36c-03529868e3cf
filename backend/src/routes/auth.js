const express = require('express');
const router = express.Router();
const bcrypt = require('bcryptjs');
const db = require('../db/init');

// Login route
router.post('/login', (req, res) => {
    const { email, password } = req.body;

    if (!email || !password) {
        return res.status(400).json({
            success: false,
            message: 'Email and password are required'
        });
    }

    db.get('SELECT * FROM users WHERE email = ?', [email], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid credentials'
            });
        }

        // Compare password
        bcrypt.compare(password, user.password_hash, (err, isMatch) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error comparing passwords'
                });
            }

            if (!isMatch) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid credentials'
                });
            }

            // Remove password hash from response
            delete user.password_hash;

            res.json({
                success: true,
                message: 'Login successful',
                user
            });
        });
    });
});

// Register route
router.post('/register', (req, res) => {
    const { full_name, email, phone, password } = req.body;

    if (!full_name || !email || !password) {
        return res.status(400).json({
            success: false,
            message: 'Full name, email and password are required'
        });
    }

    // Check if email exists
    db.get('SELECT id FROM users WHERE email = ?', [email], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (user) {
            return res.status(400).json({
                success: false,
                message: 'Email already exists'
            });
        }

        // Hash password
        bcrypt.hash(password, 10, (err, hash) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error hashing password'
                });
            }

            // Insert new user
            const role_id = 1; // Default role is customer
            db.run(
                'INSERT INTO users (full_name, email, phone, password_hash, role_id) VALUES (?, ?, ?, ?, ?)',
                [full_name, email, phone, hash, role_id],
                function(err) {
                    if (err) {
                        return res.status(500).json({
                            success: false,
                            message: 'Error creating user'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'Registration successful',
                        userId: this.lastID
                    });
                }
            );
        });
    });
});

// Get user profile
router.get('/profile/:id', (req, res) => {
    const userId = req.params.id;

    db.get('SELECT id, full_name, email, phone, role_id, created_at FROM users WHERE id = ?', [userId], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            user
        });
    });
});

// Update user profile
router.put('/profile/:id', (req, res) => {
    const userId = req.params.id;
    const { full_name, email, phone } = req.body;

    if (!full_name || !email) {
        return res.status(400).json({
            success: false,
            message: 'Full name and email are required'
        });
    }

    // Check if email is taken by another user
    db.get('SELECT id FROM users WHERE email = ? AND id != ?', [email, userId], (err, existingUser) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: 'Email is already taken by another user'
            });
        }

        // Update user profile
        db.run(
            'UPDATE users SET full_name = ?, email = ?, phone = ? WHERE id = ?',
            [full_name, email, phone, userId],
            function(err) {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error updating profile'
                    });
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        message: 'User not found'
                    });
                }

                res.json({
                    success: true,
                    message: 'Profile updated successfully'
                });
            }
        );
    });
});

// Change password
router.put('/change-password/:id', (req, res) => {
    const userId = req.params.id;
    const { current_password, new_password } = req.body;

    if (!current_password || !new_password) {
        return res.status(400).json({
            success: false,
            message: 'Current password and new password are required'
        });
    }

    // Get user's current password hash
    db.get('SELECT password_hash FROM users WHERE id = ?', [userId], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Verify current password
        bcrypt.compare(current_password, user.password_hash, (err, isMatch) => {
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Error verifying password'
                });
            }

            if (!isMatch) {
                return res.status(401).json({
                    success: false,
                    message: 'Current password is incorrect'
                });
            }

            // Hash new password
            bcrypt.hash(new_password, 10, (err, newHash) => {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error hashing new password'
                    });
                }

                // Update password
                db.run(
                    'UPDATE users SET password_hash = ? WHERE id = ?',
                    [newHash, userId],
                    function(err) {
                        if (err) {
                            return res.status(500).json({
                                success: false,
                                message: 'Error updating password'
                            });
                        }

                        res.json({
                            success: true,
                            message: 'Password changed successfully'
                        });
                    }
                );
            });
        });
    });
});

module.exports = router; 