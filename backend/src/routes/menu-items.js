const express = require('express');
const router = express.Router();
const db = require('../db/init');

// Get menu categories (must come before /:id route)
router.get('/categories/list', (req, res) => {
    db.all('SELECT DISTINCT category FROM products WHERE available = 1 ORDER BY category', [], (err, rows) => {
        if (err) {
            console.error('Error fetching categories:', err);
            return res.status(500).json({ error: 'Failed to fetch categories' });
        }
        
        const categories = rows.map(row => row.category);
        res.json({
            success: true,
            data: categories
        });
    });
});

// Get menu items statistics (must come before /:id route)
router.get('/stats/overview', (req, res) => {
    const queries = {
        total: 'SELECT COUNT(*) as count FROM products',
        active: 'SELECT COUNT(*) as count FROM products WHERE available = 1',
        inactive: 'SELECT COUNT(*) as count FROM products WHERE available = 0',
        categories: 'SELECT COUNT(DISTINCT category) as count FROM products',
        lowStock: 'SELECT COUNT(*) as count FROM products WHERE quantity_available < 10 AND available = 1'
    };
    
    const stats = {};
    let completed = 0;
    const total = Object.keys(queries).length;
    
    Object.entries(queries).forEach(([key, query]) => {
        db.get(query, [], (err, row) => {
            if (err) {
                console.error(`Error fetching ${key} stats:`, err);
                stats[key] = 0;
            } else {
                stats[key] = row.count;
            }
            
            completed++;
            if (completed === total) {
                res.json({
                    success: true,
                    data: stats
                });
            }
        });
    });
});

// Get all menu items with optional filters
router.get('/', (req, res) => {
    const { available, search, category, status } = req.query;
    
    let query = 'SELECT * FROM products WHERE 1=1';
    let params = [];
    
    if (available !== undefined) {
        query += ' AND available = ?';
        params.push(available === 'true' ? 1 : 0);
    }
    
    if (search) {
        query += ' AND (name LIKE ? OR description LIKE ? OR category LIKE ?)';
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
    }
    
    if (category) {
        query += ' AND category = ?';
        params.push(category);
    }
    
    // Note: products table doesn't have status column, but we can simulate it
    // by using available field (available=1 means active, available=0 means inactive)
    
    query += ' ORDER BY created_at DESC';
    
    db.all(query, params, (err, rows) => {
        if (err) {
            console.error('Error fetching menu items:', err);
            return res.status(500).json({ error: 'Failed to fetch menu items' });
        }
        
        // Transform data to match expected format
        const menuItems = rows.map(row => ({
            menu_item_id: row.product_id,
            name: row.name,
            description: row.description,
            price: row.price,
            category: row.category,
            image_url: row.image_url,
            available: row.available,
            status: row.available ? 'active' : 'inactive',
            quantity_available: row.quantity_available,
            created_at: row.created_at
        }));
        
        res.json({
            success: true,
            data: menuItems
        });
    });
});

// Get menu item by ID
router.get('/:id', (req, res) => {
    const { id } = req.params;
    
    db.get('SELECT * FROM products WHERE product_id = ?', [id], (err, row) => {
        if (err) {
            console.error('Error fetching menu item:', err);
            return res.status(500).json({ error: 'Failed to fetch menu item' });
        }
        
        if (!row) {
            return res.status(404).json({ error: 'Menu item not found' });
        }
        
        // Transform data to match expected format
        const menuItem = {
            menu_item_id: row.product_id,
            name: row.name,
            description: row.description,
            price: row.price,
            category: row.category,
            image_url: row.image_url,
            available: row.available,
            status: row.available ? 'active' : 'inactive',
            quantity_available: row.quantity_available,
            created_at: row.created_at
        };
        
        res.json({
            success: true,
            data: menuItem
        });
    });
});

// Create new menu item
router.post('/', (req, res) => {
    const { name, description, price, category, image_url, available = true, quantity_available = 0 } = req.body;
    
    if (!name || !price || !category) {
        return res.status(400).json({ 
            success: false,
            error: 'Name, price, and category are required' 
        });
    }
    
    if (price <= 0) {
        return res.status(400).json({ 
            success: false,
            error: 'Price must be greater than 0' 
        });
    }
    
    // Check if menu item with same name already exists
    db.get('SELECT product_id FROM products WHERE name = ?', [name], (err, existingItem) => {
        if (err) {
            console.error('Error checking existing menu item:', err);
            return res.status(500).json({ 
                success: false,
                error: 'Database error' 
            });
        }
        
        if (existingItem) {
            return res.status(409).json({ 
                success: false,
                error: 'Menu item with this name already exists' 
            });
        }
        
        // Insert new menu item
        const stmt = db.prepare(`INSERT INTO products 
            (name, description, price, category, image_url, available, quantity_available) 
            VALUES (?, ?, ?, ?, ?, ?, ?)`);
        
        stmt.run([name, description, price, category, image_url, available ? 1 : 0, quantity_available], function(err) {
            if (err) {
                console.error('Error creating menu item:', err);
                return res.status(500).json({ 
                    success: false,
                    error: 'Failed to create menu item' 
                });
            }
            
            res.status(201).json({
                success: true,
                message: 'Menu item created successfully',
                data: {
                    menu_item_id: this.lastID,
                    name,
                    description,
                    price,
                    category,
                    image_url,
                    available,
                    quantity_available
                }
            });
        });
        stmt.finalize();
    });
});

// Update menu item
router.put('/:id', (req, res) => {
    const { id } = req.params;
    const { name, description, price, category, image_url, available, quantity_available } = req.body;
    
    // Build dynamic update query based on provided fields
    let updateFields = [];
    let updateValues = [];
    
    if (name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(name);
    }
    
    if (description !== undefined) {
        updateFields.push('description = ?');
        updateValues.push(description);
    }
    
    if (price !== undefined) {
        if (price <= 0) {
            return res.status(400).json({ 
                success: false,
                error: 'Price must be greater than 0' 
            });
        }
        updateFields.push('price = ?');
        updateValues.push(price);
    }
    
    if (category !== undefined) {
        updateFields.push('category = ?');
        updateValues.push(category);
    }
    
    if (image_url !== undefined) {
        updateFields.push('image_url = ?');
        updateValues.push(image_url);
    }
    
    if (available !== undefined) {
        updateFields.push('available = ?');
        updateValues.push(available ? 1 : 0);
    }
    
    if (quantity_available !== undefined) {
        updateFields.push('quantity_available = ?');
        updateValues.push(quantity_available);
    }
    
    if (updateFields.length === 0) {
        return res.status(400).json({ 
            success: false,
            error: 'No fields to update' 
        });
    }
    
    // Add ID to the end of values array
    updateValues.push(id);
    
    const query = `UPDATE products SET ${updateFields.join(', ')} WHERE product_id = ?`;
    
    const stmt = db.prepare(query);
    stmt.run(updateValues, function(err) {
        if (err) {
            console.error('Error updating menu item:', err);
            return res.status(500).json({ 
                success: false,
                error: 'Failed to update menu item' 
            });
        }
        
        if (this.changes === 0) {
            return res.status(404).json({ 
                success: false,
                error: 'Menu item not found' 
            });
        }
        
        res.json({ 
            success: true,
            message: 'Menu item updated successfully' 
        });
    });
    stmt.finalize();
});

// Delete menu item
router.delete('/:id', (req, res) => {
    const { id } = req.params;
    
    // First check if menu item exists
    db.get('SELECT product_id FROM products WHERE product_id = ?', [id], (err, item) => {
        if (err) {
            console.error('Error checking menu item:', err);
            return res.status(500).json({ 
                success: false,
                error: 'Database error' 
            });
        }
        
        if (!item) {
            return res.status(404).json({ 
                success: false,
                error: 'Menu item not found' 
            });
        }
        
        // Check if item is referenced in orders or cart
        db.get(`SELECT COUNT(*) as count FROM order_items WHERE product_id = ? 
                UNION ALL 
                SELECT COUNT(*) as count FROM cart WHERE product_id = ?`, [id, id], (err, references) => {
            if (err) {
                console.error('Error checking references:', err);
                return res.status(500).json({ 
                    success: false,
                    error: 'Database error' 
                });
            }
            
            // If there are references, soft delete by setting available to false
            if (references && references.count > 0) {
                const stmt = db.prepare('UPDATE products SET available = 0 WHERE product_id = ?');
                stmt.run([id], function(err) {
                    if (err) {
                        console.error('Error soft deleting menu item:', err);
                        return res.status(500).json({ 
                            success: false,
                            error: 'Failed to delete menu item' 
                        });
                    }
                    
                    res.json({ 
                        success: true,
                        message: 'Menu item deactivated successfully (referenced in orders)' 
                    });
                });
                stmt.finalize();
            } else {
                // Hard delete if no references
                const stmt = db.prepare('DELETE FROM products WHERE product_id = ?');
                stmt.run([id], function(err) {
                    if (err) {
                        console.error('Error deleting menu item:', err);
                        return res.status(500).json({ 
                            success: false,
                            error: 'Failed to delete menu item' 
                        });
                    }
                    
                    res.json({ 
                        success: true,
                        message: 'Menu item deleted successfully' 
                    });
                });
                stmt.finalize();
            }
        });
    });
});

// Bulk update availability
router.patch('/bulk/availability', (req, res) => {
    const { product_ids, available } = req.body;
    
    if (!Array.isArray(product_ids) || product_ids.length === 0) {
        return res.status(400).json({ 
            success: false,
            error: 'Product IDs array is required' 
        });
    }
    
    if (available === undefined) {
        return res.status(400).json({ 
            success: false,
            error: 'Available status is required' 
        });
    }
    
    const placeholders = product_ids.map(() => '?').join(',');
    const query = `UPDATE products SET available = ? WHERE product_id IN (${placeholders})`;
    const params = [available ? 1 : 0, ...product_ids];
    
    const stmt = db.prepare(query);
    stmt.run(params, function(err) {
        if (err) {
            console.error('Error bulk updating availability:', err);
            return res.status(500).json({ 
                success: false,
                error: 'Failed to update availability' 
            });
        }
        
        res.json({ 
            success: true,
            message: `Updated ${this.changes} menu items`,
            updated_count: this.changes
        });
    });
    stmt.finalize();
});

module.exports = router;
