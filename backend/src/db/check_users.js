const db = require('./init');

console.log('Checking user passwords...');

db.all("SELECT user_id, full_name, email, password, role FROM users", (err, users) => {
    if (err) {
        console.error('Error getting users:', err);
    } else {
        console.log('All users with passwords:');
        users.forEach(user => {
            console.log(`- ${user.user_id}: ${user.full_name} (${user.email}) - Password: ${user.password} - Role: ${user.role}`);
        });
    }
    process.exit(0);
});
