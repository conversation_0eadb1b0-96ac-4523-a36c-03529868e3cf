const db = require('./init');

// Insert test users to match the Android app's TestUsers
const testUsers = [
    {
        full_name: 'Test Customer',
        email: '<EMAIL>',
        phone: '1234567890',
        password: 'password123',
        role_id: 1 // customer
    },
    {
        full_name: 'Test Barista',
        email: '<EMAIL>', 
        phone: '1234567891',
        password: 'password123',
        role_id: 2 // barista
    },
    {
        full_name: 'Test Shipper',
        email: '<EMAIL>',
        phone: '1234567892', 
        password: 'password123',
        role_id: 3 // shipper
    },
    {
        full_name: 'Test Manager',
        email: '<EMAIL>',
        phone: '1234567893',
        password: 'password123',
        role_id: 4 // manager
    },
    {
        full_name: 'Test Support',
        email: '<EMAIL>',
        phone: '1234567894',
        password: 'password123',
        role_id: 5 // support
    }
];

console.log('Inserting test users...');

testUsers.forEach((user, index) => {
    db.run(`INSERT OR REPLACE INTO users (full_name, email, phone, password, role_id) 
            VALUES (?, ?, ?, ?, ?)`,
        [user.full_name, user.email, user.phone, user.password, user.role_id],
        function(err) {
            if (err) {
                console.error(`Error inserting user ${user.email}:`, err);
            } else {
                console.log(`✅ Inserted user: ${user.email} (${user.full_name})`);
            }
            
            // Close database after last user
            if (index === testUsers.length - 1) {
                setTimeout(() => {
                    console.log('Test users insertion completed!');
                    process.exit(0);
                }, 100);
            }
        }
    );
});
