const sqlite3 = require('sqlite3').verbose();
const path = require('path');

// Create database connection
const db = new sqlite3.Database(path.join(__dirname, 'coffee_shop.db'), (err) => {
    if (err) {
        console.error('Error connecting to database:', err);
        return;
    }
    console.log('Connected to SQLite database');
});

// Enable foreign key support
db.run('PRAGMA foreign_keys = ON');

// Create tables
db.serialize(() => {
    // Users table
    db.run(`CREATE TABLE IF NOT EXISTS users (
        user_id INTEGER PRIMARY KEY AUTOINCREMENT,
        full_name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT,
        password TEXT NOT NULL,
        role TEXT DEFAULT 'customer',
<<<<<<< HEAD
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
=======
        is_active BOOLEAN DEFAULT 1,
        date_of_birth DATE,
        address TEXT,
        city TEXT,
        profile_image_url TEXT,
        last_login DATETIME,
        email_verified BOOLEAN DEFAULT 0,
        phone_verified BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
    )`);

    // Products table
    db.run(`CREATE TABLE IF NOT EXISTS products (
        product_id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        category TEXT NOT NULL,
        available BOOLEAN DEFAULT 1,
        quantity_available INTEGER DEFAULT 0,
        image_url TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`);

    // Cart table
    db.run(`CREATE TABLE IF NOT EXISTS cart (
        cart_id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(user_id),
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )`);

    // Orders table
    db.run(`CREATE TABLE IF NOT EXISTS orders (
        order_id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        customer_name TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        total_price DECIMAL(10,2) NOT NULL,
        delivery_address TEXT,
        special_instructions TEXT,
        payment_method TEXT DEFAULT 'cash',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES users(user_id)
    )`);

    // Order Items table
    db.run(`CREATE TABLE IF NOT EXISTS order_items (
        order_item_id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        product_id INTEGER NOT NULL,
        product_name TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY (order_id) REFERENCES orders(order_id),
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )`);

<<<<<<< HEAD
    // Daily Tasks table
    db.run(`CREATE TABLE IF NOT EXISTS daily_tasks (
        task_id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT,
        estimated_time TEXT,
        priority TEXT,
        assigned_to INTEGER,
        status TEXT DEFAULT 'pending',
        due_date DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assigned_to) REFERENCES users(user_id)
    )`);

    // Inventory table
    db.run(`CREATE TABLE IF NOT EXISTS inventory_items (
        item_id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unit TEXT NOT NULL,
        reorder_level INTEGER,
        last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_by INTEGER,
        FOREIGN KEY (updated_by) REFERENCES users(user_id)
    )`);

    // Recipes table
    db.run(`CREATE TABLE IF NOT EXISTS recipes (
        recipe_id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        preparation_time INTEGER,
        difficulty_level TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(product_id)
    )`);

    // Recipe Steps table
    db.run(`CREATE TABLE IF NOT EXISTS recipe_steps (
        step_id INTEGER PRIMARY KEY AUTOINCREMENT,
        recipe_id INTEGER NOT NULL,
        step_number INTEGER NOT NULL,
        description TEXT NOT NULL,
        time_required INTEGER,
        FOREIGN KEY (recipe_id) REFERENCES recipes(recipe_id)
    )`);

=======
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
    // Insert sample customer (no password hashing as requested)
    db.run(`INSERT OR IGNORE INTO users (user_id, full_name, email, phone, password, role) 
            VALUES (1, 'John Customer', '<EMAIL>', '************', 'password123', 'customer')`);

    // Insert sample products
    const products = [
        ['Espresso', 'Rich and bold single shot espresso', 2.50, 'Espresso', 1, 50, 'https://example.com/espresso.jpg'],
        ['Double Espresso', 'Double shot of rich espresso', 3.50, 'Espresso', 1, 50, 'https://example.com/double-espresso.jpg'],
        ['Americano', 'Espresso with hot water', 3.00, 'Espresso', 1, 45, 'https://example.com/americano.jpg'],
        ['Cappuccino', 'Espresso with steamed milk and thick foam', 4.00, 'Milk Coffee', 1, 40, 'https://example.com/cappuccino.jpg'],
        ['Latte', 'Espresso with steamed milk and light foam', 4.50, 'Milk Coffee', 1, 35, 'https://example.com/latte.jpg'],
        ['Flat White', 'Double shot espresso with steamed milk', 4.25, 'Milk Coffee', 1, 30, 'https://example.com/flat-white.jpg'],
        ['Mocha', 'Espresso with chocolate and steamed milk', 5.00, 'Milk Coffee', 1, 20, 'https://example.com/mocha.jpg'],
        ['Iced Coffee', 'Cold brew coffee over ice', 3.50, 'Cold Drinks', 1, 30, 'https://example.com/iced-coffee.jpg'],
        ['Iced Latte', 'Chilled espresso with cold milk', 4.75, 'Cold Drinks', 1, 25, 'https://example.com/iced-latte.jpg'],
        ['Frappuccino', 'Blended coffee with ice and whipped cream', 5.50, 'Cold Drinks', 1, 25, 'https://example.com/frappuccino.jpg'],
        ['Caramel Macchiato', 'Vanilla latte with caramel drizzle', 5.25, 'Specialty', 1, 15, 'https://example.com/caramel-macchiato.jpg'],
        ['Chai Latte', 'Spiced tea with steamed milk', 4.50, 'Specialty', 1, 18, 'https://example.com/chai-latte.jpg'],
        ['Hot Chocolate', 'Rich chocolate with steamed milk', 3.75, 'Specialty', 1, 22, 'https://example.com/hot-chocolate.jpg'],
        ['Croissant', 'Buttery, flaky French pastry', 3.25, 'Pastries', 1, 20, 'https://example.com/croissant.jpg'],
        ['Blueberry Muffin', 'Fresh blueberry muffin', 2.75, 'Pastries', 1, 15, 'https://example.com/muffin.jpg'],
        ['Chocolate Chip Cookie', 'Homemade chocolate chip cookie', 2.00, 'Snacks', 1, 25, 'https://example.com/cookie.jpg']
    ];

    const insertProduct = db.prepare(`INSERT OR IGNORE INTO products 
        (name, description, price, category, available, quantity_available, image_url) 
        VALUES (?, ?, ?, ?, ?, ?, ?)`);
    
    products.forEach(product => {
        insertProduct.run(product);
    });
    insertProduct.finalize();
<<<<<<< HEAD

    // Insert sample barista user
    db.run(`INSERT OR IGNORE INTO users (user_id, full_name, email, phone, password, role) 
            VALUES (2, 'Maria Barista', '<EMAIL>', '************', 'barista123', 'barista')`);

    // Insert sample inventory items
    const inventoryItems = [
        ['Coffee Beans (Espresso)', 45, 'kg', 10],
        ['Coffee Beans (House Blend)', 38, 'kg', 10],
        ['Milk', 25, 'liters', 5],
        ['Almond Milk', 8, 'liters', 3],
        ['Oat Milk', 12, 'liters', 3],
        ['Sugar', 20, 'kg', 5],
        ['Vanilla Syrup', 15, 'bottles', 3],
        ['Caramel Syrup', 12, 'bottles', 3],
        ['Chocolate Syrup', 10, 'bottles', 3],
        ['Whipped Cream', 18, 'cans', 4],
        ['Paper Cups (Small)', 150, 'pieces', 50],
        ['Paper Cups (Medium)', 120, 'pieces', 40],
        ['Paper Cups (Large)', 100, 'pieces', 30],
        ['Lids', 200, 'pieces', 60],
        ['Napkins', 500, 'pieces', 100],
        ['Stirrers', 300, 'pieces', 80]
    ];

    const insertInventory = db.prepare(`INSERT OR IGNORE INTO inventory_items 
        (name, quantity, unit, reorder_level) VALUES (?, ?, ?, ?)`);
    
    inventoryItems.forEach(item => {
        insertInventory.run(item);
    });
    insertInventory.finalize();

    // Insert sample daily tasks
    const dailyTasks = [
        ['Clean Coffee Machine', 'Daily maintenance and cleaning of the espresso machine', '15 minutes', 'high'],
        ['Restock Cups and Napkins', 'Check and refill cups, napkins, and other supplies', '10 minutes', 'medium'],
        ['Check Milk Inventory', 'Verify milk levels and freshness dates', '5 minutes', 'high'],
        ['Sanitize Work Surfaces', 'Clean and sanitize all work areas and counters', '20 minutes', 'high'],
        ['Update Menu Board', 'Check daily specials and update menu displays', '10 minutes', 'low'],
        ['Organize Storage Area', 'Tidy up storage and check for expired items', '15 minutes', 'medium'],
        ['Check Equipment', 'Inspect grinders, brewers, and other equipment', '10 minutes', 'medium'],
        ['Count Register', 'Count cash drawer and record daily sales', '15 minutes', 'high']
    ];

    const insertTask = db.prepare(`INSERT OR IGNORE INTO daily_tasks 
        (title, description, estimated_time, priority) VALUES (?, ?, ?, ?)`);
    
    dailyTasks.forEach(task => {
        insertTask.run(task);
    });
    insertTask.finalize();

    // Insert sample recipes
    const recipes = [
        [1, 'Espresso', 'Rich and bold single shot espresso', 30, 'easy'],
        [2, 'Cappuccino', 'Espresso with steamed milk and thick foam', 120, 'medium'],
        [3, 'Latte', 'Espresso with steamed milk and light foam', 90, 'easy'],
        [4, 'Americano', 'Espresso with hot water', 45, 'easy'],
        [7, 'Mocha', 'Espresso with chocolate and steamed milk', 150, 'medium'],
        [11, 'Caramel Macchiato', 'Vanilla latte with caramel drizzle', 180, 'hard']
    ];

    const insertRecipe = db.prepare(`INSERT OR IGNORE INTO recipes 
        (product_id, name, description, preparation_time, difficulty_level) VALUES (?, ?, ?, ?, ?)`);
    
    recipes.forEach(recipe => {
        insertRecipe.run(recipe);
    });
    insertRecipe.finalize();

    // Insert sample recipe steps
    const recipeSteps = [
        // Espresso steps
        [1, 1, 'Grind coffee beans to a fine consistency', 10],
        [1, 2, 'Tamp the coffee grounds into the portafilter', 5],
        [1, 3, 'Brew the espresso shot', 25],
        
        // Cappuccino steps
        [2, 1, 'Grind coffee beans to a fine consistency', 10],
        [2, 2, 'Tamp the coffee grounds into the portafilter', 5],
        [2, 3, 'Brew the espresso shot', 25],
        [2, 4, 'Steam milk to create thick foam', 60],
        [2, 5, 'Pour steamed milk over espresso', 20],
        
        // Latte steps
        [3, 1, 'Grind coffee beans to a fine consistency', 10],
        [3, 2, 'Tamp the coffee grounds into the portafilter', 5],
        [3, 3, 'Brew the espresso shot', 25],
        [3, 4, 'Steam milk with light foam', 40],
        [3, 5, 'Pour steamed milk over espresso', 10],
        
        // Americano steps
        [4, 1, 'Grind coffee beans to a fine consistency', 10],
        [4, 2, 'Tamp the coffee grounds into the portafilter', 5],
        [4, 3, 'Brew the espresso shot', 25],
        [4, 4, 'Add hot water to espresso', 5],
        
        // Mocha steps
        [5, 1, 'Grind coffee beans to a fine consistency', 10],
        [5, 2, 'Tamp the coffee grounds into the portafilter', 5],
        [5, 3, 'Brew the espresso shot', 25],
        [5, 4, 'Add chocolate syrup to cup', 10],
        [5, 5, 'Steam milk', 60],
        [5, 6, 'Pour steamed milk over chocolate and espresso', 20],
        [5, 7, 'Top with whipped cream', 20],
        
        // Caramel Macchiato steps
        [6, 1, 'Add vanilla syrup to cup', 10],
        [6, 2, 'Steam milk', 60],
        [6, 3, 'Pour steamed milk into cup', 20],
        [6, 4, 'Grind coffee beans to a fine consistency', 10],
        [6, 5, 'Tamp the coffee grounds into the portafilter', 5],
        [6, 6, 'Brew espresso shot on top of milk', 25],
        [6, 7, 'Drizzle caramel sauce on top', 30],
        [6, 8, 'Create foam art if desired', 20]
    ];

    const insertStep = db.prepare(`INSERT OR IGNORE INTO recipe_steps 
        (recipe_id, step_number, description, time_required) VALUES (?, ?, ?, ?)`);
    
    recipeSteps.forEach(step => {
        insertStep.run(step);
    });
    insertStep.finalize();
=======
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
});

// Export database connection
module.exports = db; 