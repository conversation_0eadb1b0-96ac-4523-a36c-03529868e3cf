const express = require('express');
const cors = require('cors');
const db = require('./db/init');

const app = express();
app.use(cors());
app.use(express.json());
// ==================== AUTH ROUTES ====================

// Login
app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    
    if (!email || !password) {
        return res.status(400).json({ error: 'Email and password are required' });
    }

    db.get('SELECT * FROM users WHERE email = ? AND password = ?', [email, password], (err, user) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        if (!user) {
            return res.status(401).json({ error: 'Invalid email or password' });
        }
        res.json({
            user_id: user.user_id,
            full_name: user.full_name,
            email: user.email,
            phone: user.phone,
            role: user.role || 'customer'
        });
    });
});

// Register
app.post('/api/auth/register', (req, res) => {
    const { full_name, email, phone, password } = req.body;
    
    if (!full_name || !email || !password) {
        return res.status(400).json({ error: 'Full name, email and password are required' });
    }

    // Check if email already exists
    db.get('SELECT email FROM users WHERE email = ?', [email], (err, existingUser) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        if (existingUser) {
            return res.status(400).json({ error: 'Email already registered' });
        }

        // Insert new user
        db.run('INSERT INTO users (full_name, email, phone, password, role) VALUES (?, ?, ?, ?, ?)',
            [full_name, email, phone || '', password, 'customer'], // default role is customer
            function(err) {
                if (err) {
                    return res.status(500).json({ error: 'Registration failed' });
                }
                res.json({
                    user_id: this.lastID,
                    full_name,
                    email,
                    phone: phone || '',
                    role: 'customer'
                });
            });
    });
});

// Get user profile
app.get('/api/auth/profile/:id', (req, res) => {
    const userId = req.params.id;

    db.get('SELECT user_id, full_name, email, phone, role FROM users WHERE user_id = ?', [userId], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            user: {
                id: user.user_id,
                full_name: user.full_name,
                email: user.email,
                phone: user.phone,

                role: user.role

                role_id: user.role === 'customer' ? 1 : 2

            }
        });
    });
});

// Update user profile
app.put('/api/auth/profile/:id', (req, res) => {
    const userId = req.params.id;
    const { full_name, email, phone } = req.body;

    if (!full_name || !email) {
        return res.status(400).json({
            success: false,
            message: 'Full name and email are required'
        });
    }

    // Check if email is taken by another user
    db.get('SELECT user_id FROM users WHERE email = ? AND user_id != ?', [email, userId], (err, existingUser) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: 'Email is already taken by another user'
            });
        }

        // Update user profile
        db.run(
            'UPDATE users SET full_name = ?, email = ?, phone = ? WHERE user_id = ?',
            [full_name, email, phone, userId],
            function(err) {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error updating profile'
                    });
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        message: 'User not found'
                    });
                }

                res.json({
                    success: true,
                    message: 'Profile updated successfully'
                });
            }
        );
    });
});

// Change password
app.put('/api/auth/change-password/:id', (req, res) => {
    const userId = req.params.id;
    const { current_password, new_password } = req.body;

    if (!current_password || !new_password) {
        return res.status(400).json({
            success: false,
            message: 'Current password and new password are required'
        });
    }

    // Get user's current password
    db.get('SELECT password FROM users WHERE user_id = ?', [userId], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check current password (simple comparison since we're not using hashing in this implementation)
        if (user.password !== current_password) {
            return res.status(401).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }

        // Update password
        db.run(
            'UPDATE users SET password = ? WHERE user_id = ?',
            [new_password, userId],
            function(err) {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error updating password'
                    });
                }

                res.json({
                    success: true,
                    message: 'Password changed successfully'
                });
            }
        );
    });
});

// ==================== PRODUCTS ROUTES ====================

// Get all products
app.get('/api/products', (req, res) => {
    db.all('SELECT * FROM products WHERE available = 1', [], (err, products) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(products);
    });
});

// Get products by category
app.get('/api/products/category/:category', (req, res) => {
    const { category } = req.params;
    db.all('SELECT * FROM products WHERE category = ? AND available = 1', [category], (err, products) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(products);
    });
});

// Get product categories
app.get('/api/products/categories', (req, res) => {
    db.all('SELECT DISTINCT category FROM products WHERE available = 1', [], (err, categories) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        const categoryList = ['All', ...categories.map(c => c.category)];
        res.json(categoryList);
    });
});

// ==================== CART ROUTES ====================

// Get cart items for user
app.get('/api/cart/:userId', (req, res) => {
    const { userId } = req.params;
    
    db.all(`SELECT c.*, p.name, p.description, p.image_url 
<<<<<<< HEAD
            FROM cart_items c 
            JOIN products p ON c.product_id = p.item_id 
=======
            FROM cart c 
            JOIN products p ON c.product_id = p.product_id 
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
            WHERE c.user_id = ?`, [userId], (err, cartItems) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(cartItems);
    });
});

// Add item to cart
app.post('/api/cart', (req, res) => {
    const { user_id, product_id, quantity } = req.body;
    
    if (!user_id || !product_id || !quantity) {
        return res.status(400).json({ error: 'User ID, product ID and quantity are required' });
    }

    // Get product price
    db.get('SELECT price, quantity_available FROM products WHERE product_id = ?', [product_id], (err, product) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        if (!product) {
            return res.status(404).json({ error: 'Product not found' });
        }
        if (product.quantity_available < quantity) {
            return res.status(400).json({ error: 'Not enough stock available' });
        }

        // Check if item already exists in cart
        db.get('SELECT * FROM cart WHERE user_id = ? AND product_id = ?', [user_id, product_id], (err, existingItem) => {
            if (err) {
                return res.status(500).json({ error: 'Database error' });
            }

            if (existingItem) {
                // Update quantity
                const newQuantity = existingItem.quantity + quantity;
                if (newQuantity > product.quantity_available) {
                    return res.status(400).json({ error: 'Not enough stock available' });
                }
                
                db.run('UPDATE cart SET quantity = ? WHERE cart_id = ?', [newQuantity, existingItem.cart_id], (err) => {
                    if (err) {
                        return res.status(500).json({ error: 'Failed to update cart' });
                    }
                    res.json({ message: 'Cart updated successfully' });
                });
            } else {
                // Add new item
                db.run('INSERT INTO cart (user_id, product_id, quantity, unit_price) VALUES (?, ?, ?, ?)', 
                    [user_id, product_id, quantity, product.price], (err) => {
                        if (err) {
                            return res.status(500).json({ error: 'Failed to add to cart' });
                        }
                        res.json({ message: 'Item added to cart successfully' });
                    });
            }
        });
    });
});

// Update cart item quantity
app.put('/api/cart/:cartId', (req, res) => {
    const { cartId } = req.params;
    const { quantity } = req.body;
    
    if (quantity <= 0) {
        // Remove item if quantity is 0 or less
        db.run('DELETE FROM cart WHERE cart_id = ?', [cartId], (err) => {
            if (err) {
                return res.status(500).json({ error: 'Failed to remove item' });
            }
            res.json({ message: 'Item removed from cart' });
        });
    } else {
        db.run('UPDATE cart SET quantity = ? WHERE cart_id = ?', [quantity, cartId], (err) => {
            if (err) {
                return res.status(500).json({ error: 'Failed to update cart' });
            }
            res.json({ message: 'Cart updated successfully' });
        });
    }
});

// Remove item from cart
app.delete('/api/cart/:cartId', (req, res) => {
    const { cartId } = req.params;
    
    db.run('DELETE FROM cart WHERE cart_id = ?', [cartId], (err) => {
        if (err) {
            return res.status(500).json({ error: 'Failed to remove item' });
        }
        res.json({ message: 'Item removed from cart' });
    });
});

// Clear cart
app.delete('/api/cart/user/:userId', (req, res) => {
    const { userId } = req.params;
    
    db.run('DELETE FROM cart WHERE user_id = ?', [userId], (err) => {
        if (err) {
            return res.status(500).json({ error: 'Failed to clear cart' });
        }
        res.json({ message: 'Cart cleared successfully' });
    });
});

// ==================== ORDERS ROUTES ====================

// Get orders for user
app.get('/api/orders/user/:userId', (req, res) => {
    const { userId } = req.params;
    
    db.all(`SELECT o.*, 
<<<<<<< HEAD
            GROUP_CONCAT(p.name || ' x' || oi.quantity) as items_summary
            FROM orders o 
            LEFT JOIN order_items oi ON o.order_id = oi.order_id 
            LEFT JOIN products p ON oi.product_id = p.item_id
=======
            GROUP_CONCAT(oi.product_name || ' x' || oi.quantity) as items_summary
            FROM orders o 
            LEFT JOIN order_items oi ON o.order_id = oi.order_id 
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
            WHERE o.customer_id = ? 
            GROUP BY o.order_id 
            ORDER BY o.created_at DESC`, [userId], (err, orders) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(orders);
    });
});

<<<<<<< HEAD
// Get all orders (for baristas)
app.get('/api/orders', (req, res) => {
    const { status } = req.query; // Optional status filter
    
    let query = `SELECT o.*, 
                 GROUP_CONCAT(p.name || ' x' || oi.quantity) as items_summary
                 FROM orders o 
                 LEFT JOIN order_items oi ON o.order_id = oi.order_id
                 LEFT JOIN products p ON oi.product_id = p.item_id`;
    let params = [];
    
    if (status && status !== 'all') {
        query += ' WHERE o.status = ?';
        params.push(status);
    }
    
    query += ' GROUP BY o.order_id ORDER BY o.created_at DESC';
    
    db.all(query, params, (err, orders) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(orders);
    });
});

// Get active orders (pending, preparing, ready)
app.get('/api/orders/active', (req, res) => {
    db.all(`SELECT * FROM orders WHERE status IN ('pending', 'preparing', 'ready') ORDER BY order_date ASC`, (err, orders) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(orders);
    });
});

// Get ready orders for pickup
app.get('/api/orders/ready', (req, res) => {
    db.all(`SELECT * FROM orders WHERE status = 'ready' ORDER BY order_date ASC`, (err, orders) => {
        if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(orders);
    });
});

=======
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
// Get single order with items
app.get('/api/orders/:orderId', (req, res) => {
    const { orderId } = req.params;
    
    db.get('SELECT * FROM orders WHERE order_id = ?', [orderId], (err, order) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        if (!order) {
            return res.status(404).json({ error: 'Order not found' });
        }

        db.all('SELECT * FROM order_items WHERE order_id = ?', [orderId], (err, items) => {
            if (err) {
                return res.status(500).json({ error: 'Database error' });
            }
            order.items = items;
            res.json(order);
        });
    });
});

// Create new order
app.post('/api/orders', (req, res) => {
    const { customer_id, customer_name, total_price, delivery_address, special_instructions, payment_method, items } = req.body;
    
    if (!customer_id || !customer_name || !total_price || !items || items.length === 0) {
        return res.status(400).json({ error: 'Missing required order information' });
    }

    db.run('INSERT INTO orders (customer_id, customer_name, total_price, delivery_address, special_instructions, payment_method) VALUES (?, ?, ?, ?, ?, ?)', 
        [customer_id, customer_name, total_price, delivery_address || '', special_instructions || '', payment_method || 'cash'], 
        function(err) {
            if (err) {
                return res.status(500).json({ error: 'Failed to create order' });
            }

            const orderId = this.lastID;
            let itemsProcessed = 0;
            
            // Insert order items
            items.forEach(item => {
<<<<<<< HEAD
                db.run('INSERT INTO order_items (order_id, product_id, quantity, unit_price) VALUES (?, ?, ?, ?)', 
                    [orderId, item.product_id, item.quantity, item.unit_price], 
=======
                db.run('INSERT INTO order_items (order_id, product_id, product_name, quantity, unit_price) VALUES (?, ?, ?, ?, ?)', 
                    [orderId, item.product_id, item.product_name, item.quantity, item.unit_price], 
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
                    (err) => {
                        if (err) {
                            return res.status(500).json({ error: 'Failed to create order items' });
                        }
                        itemsProcessed++;
                        if (itemsProcessed === items.length) {
                            res.json({ 
                                order_id: orderId, 
                                message: 'Order created successfully' 
                            });
                        }
                    });
            });
        });
});

// Update order status
app.put('/api/orders/:orderId/status', (req, res) => {
    const { orderId } = req.params;
    const { status } = req.body;
    
    db.run('UPDATE orders SET status = ? WHERE order_id = ?', [status, orderId], (err) => {
        if (err) {
            return res.status(500).json({ error: 'Failed to update order status' });
        }
        res.json({ message: 'Order status updated successfully' });
    });
});

<<<<<<< HEAD
// Get all orders (for baristas)
app.get('/api/orders', (req, res) => {
    const { status } = req.query; // Optional status filter
    
    let query = `SELECT o.*, 
                 GROUP_CONCAT(p.name || ' x' || oi.quantity) as items_summary
                 FROM orders o 
                 LEFT JOIN order_items oi ON o.order_id = oi.order_id
                 LEFT JOIN products p ON oi.product_id = p.item_id`;
    let params = [];
    
    if (status && status !== 'all') {
        query += ' WHERE o.status = ?';
        params.push(status);
    }
    
    query += ' GROUP BY o.order_id ORDER BY o.created_at DESC';
    
    db.all(query, params, (err, orders) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(orders);
    });
});

// Get orders by status (for baristas)
app.get('/api/orders/status/:status', (req, res) => {
    const { status } = req.params;
    
    db.all(`SELECT o.*, 
            GROUP_CONCAT(p.name || ' x' || oi.quantity) as items_summary
            FROM orders o 
            LEFT JOIN order_items oi ON o.order_id = oi.order_id 
            LEFT JOIN products p ON oi.product_id = p.product_id
            WHERE o.status = ? 
            GROUP BY o.order_id 
            ORDER BY o.order_date ASC`, [status], (err, orders) => {
        if (err) {
            console.error('Error in /api/orders/status/:status:', err);
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(orders);
    });
});

// ==================== BARISTA ROUTES ====================

// Get all inventory items
app.get('/api/inventory', (req, res) => {
    db.all('SELECT * FROM inventory_items ORDER BY name', (err, items) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(items);
    });
});

// Get low stock items
app.get('/api/inventory/low-stock', (req, res) => {
    db.all('SELECT * FROM inventory_items WHERE quantity <= reorder_level ORDER BY name', (err, items) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(items);
    });
});

// Get out of stock items
app.get('/api/inventory/out-of-stock', (req, res) => {
    db.all('SELECT * FROM inventory_items WHERE quantity = 0 ORDER BY name', (err, items) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(items);
    });
});

// Update inventory item stock
app.put('/api/inventory/:id/stock', (req, res) => {
    const { id } = req.params;
    const { quantity } = req.body;
    
    if (quantity < 0) {
        return res.status(400).json({ error: 'Quantity cannot be negative' });
    }
    
    db.run('UPDATE inventory_items SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE item_id = ?', 
           [quantity, id], function(err) {
        if (err) {
            return res.status(500).json({ error: 'Failed to update inventory' });
        }
        if (this.changes === 0) {
            return res.status(404).json({ error: 'Inventory item not found' });
        }
        res.json({ message: 'Inventory updated successfully' });
    });
});

// Get all daily tasks
app.get('/api/tasks', (req, res) => {
    db.all('SELECT * FROM daily_tasks ORDER BY priority DESC, created_at', (err, tasks) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(tasks);
    });
});

// Update task completion status
app.put('/api/tasks/:id/complete', (req, res) => {
    const { id } = req.params;
    const { completed } = req.body;
    const status = completed ? 'completed' : 'pending';
    
    db.run('UPDATE daily_tasks SET status = ? WHERE task_id = ?', [status, id], function(err) {
        if (err) {
            return res.status(500).json({ error: 'Failed to update task' });
        }
        if (this.changes === 0) {
            return res.status(404).json({ error: 'Task not found' });
        }
        res.json({ message: 'Task status updated successfully' });
    });
});

// Get all recipes
app.get('/api/recipes', (req, res) => {
    const sql = `
        SELECT r.*, 
               GROUP_CONCAT(rs.description, '|' || rs.time_required ORDER BY rs.step_number) as steps
        FROM recipes r
        LEFT JOIN recipe_steps rs ON r.recipe_id = rs.recipe_id
        GROUP BY r.recipe_id
        ORDER BY r.name
    `;
    
    db.all(sql, (err, recipes) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        
        // Parse steps into array format
        const formattedRecipes = recipes.map(recipe => ({
            ...recipe,
            steps: recipe.steps ? recipe.steps.split(',').map(step => {
                const [description, time] = step.split('|');
                return { description, time_required: parseInt(time) || 0 };
            }) : []
        }));
        
        res.json(formattedRecipes);
    });
});

// Get recipe categories
app.get('/api/recipes/categories', (req, res) => {
    db.all('SELECT DISTINCT difficulty_level as category FROM recipes WHERE difficulty_level IS NOT NULL', (err, categories) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(categories.map(c => c.category));
    });
});

// Get recipes by category
app.get('/api/recipes/category/:category', (req, res) => {
    const { category } = req.params;
    
    const sql = `
        SELECT r.*, 
               GROUP_CONCAT(rs.description, '|' || rs.time_required ORDER BY rs.step_number) as steps
        FROM recipes r
        LEFT JOIN recipe_steps rs ON r.recipe_id = rs.recipe_id
        WHERE r.difficulty_level = ?
        GROUP BY r.recipe_id
        ORDER BY r.name
    `;
    
    db.all(sql, [category], (err, recipes) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        
        // Parse steps into array format
        const formattedRecipes = recipes.map(recipe => ({
            ...recipe,
            steps: recipe.steps ? recipe.steps.split(',').map(step => {
                const [description, time] = step.split('|');
                return { description, time_required: parseInt(time) || 0 };
            }) : []
        }));
        
        res.json(formattedRecipes);
    });
});

=======
app.get('/api/orders-assigned', (req, res) => {
    const sql = `
        SELECT
            o.order_id,
            o.customer_id,
            u.phone AS customer_phone,
            o.customer_name,
            o.status,
            o.total_price,
            o.delivery_address,
            o.special_instructions,
            o.payment_method,
            o.created_at,
            GROUP_CONCAT(oi.product_name || ' x' || oi.quantity, ', ') AS items
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.user_id
        LEFT JOIN order_items oi ON o.order_id = oi.order_id
        WHERE o.status = 'pending'
        GROUP BY o.order_id
        ORDER BY o.created_at DESC
    `;


    db.all(sql, [], (err, rows) => {
        if (err) {
            console.error('SQL error:', err.message);
            return res.status(500).json({ error: 'Internal database error', details: err.message });
        }

        res.json(rows);
    });
});

app.get('/api/shipper-earnings', (req, res) => {
    const sql = `
        SELECT
            COUNT(*) AS completed_orders,
            SUM(total_price) AS total_earnings,
            AVG(total_price) AS average_per_order
        FROM orders
        WHERE status = 'completed'
    `;

    db.get(sql, [], (err, row) => {
        if (err) {
            console.error(err);
            return res.status(500).json({ error: 'Database error' });
        }

        res.json({
            completed_orders: row.completed_orders || 0,
            total_earnings: row.total_earnings || 0,
            average_per_order: row.average_per_order || 0
        });
    });
});

app.get('/api/orders-completed', (req, res) => {
  const sql = `
      SELECT
        order_id AS id,
        customer_name,
        total_price,
        created_at AS completed_at
      FROM orders
      WHERE status = 'completed'
      ORDER BY completed_at DESC
    `;

  db.all(sql, [], (err, rows) => {
    if (err) {
      console.error('❌ DB query error', err);
      return res.status(500).json({ error: 'Failed to fetch completed orders' });
    }

    res.json(rows);
  });
});

app.get('/api/orders-detail/:id', (req, res) => {
    const { id } = req.params;

    const sql = `
        SELECT
            o.order_id,
            o.customer_id,
            o.customer_name,
            o.delivery_address,
            o.total_price,
            o.status,
            o.created_at,
            o.special_instructions,
            o.payment_method,
            c.phone AS customer_phone
        FROM orders o
        LEFT JOIN users c ON o.customer_id = c.user_id
        WHERE o.order_id = ?
    `;

    db.get(sql, [id], (err, row) => {
        if (err) {
            console.error('[DB ERROR]', err.message);
            return res.status(500).json({ error: 'Internal server error', details: err.message });
        }

        if (!row) {
            return res.status(404).json({ error: 'Order not found' });
        }

        // Optional: format response if needed
        res.json({
            order_id: row.order_id,
            customer_id: row.customer_id,
            customer_name: row.customer_name,
            delivery_address: row.delivery_address,
            total_price: row.total_price,
            status: row.status,
            created_at: row.created_at,
            special_instructions: row.special_instructions || '',
            payment_method: row.payment_method,
            customer_phone: row.customer_phone || ''
        });
    });
});
>>>>>>> 04bb5bde74ef42d64020056cfac32be2e0fc6c8a
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Coffee Shop API server running on port ${PORT}`);
});
