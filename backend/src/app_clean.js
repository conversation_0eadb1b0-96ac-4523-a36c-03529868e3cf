const express = require('express');
const cors = require('cors');
const db = require('./db/init');

const app = express();
app.use(cors());
app.use(express.json());

// ==================== AUTH ROUTES ====================

// Login
app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    
    if (!email || !password) {
        return res.status(400).json({ error: 'Email and password are required' });
    }

    db.get('SELECT * FROM users WHERE email = ? AND password = ?', [email, password], (err, user) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        if (!user) {
            return res.status(401).json({ error: 'Invalid email or password' });
        }
        res.json({
            user_id: user.user_id,
            full_name: user.full_name,
            email: user.email,
            phone: user.phone,
            role: user.role || 'customer'
        });
    });
});

// Register
app.post('/api/auth/register', (req, res) => {
    const { full_name, email, phone, password } = req.body;
    
    if (!full_name || !email || !password) {
        return res.status(400).json({ error: 'Full name, email and password are required' });
    }

    // Check if email already exists
    db.get('SELECT email FROM users WHERE email = ?', [email], (err, existingUser) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        if (existingUser) {
            return res.status(400).json({ error: 'Email already registered' });
        }

        // Insert new user
        db.run('INSERT INTO users (full_name, email, phone, password, role) VALUES (?, ?, ?, ?, ?)', 
            [full_name, email, phone || '', password, 'customer'], // default role is customer
            function(err) {
                if (err) {
                    return res.status(500).json({ error: 'Registration failed' });
                }
                res.json({
                    user_id: this.lastID,
                    full_name,
                    email,
                    phone: phone || '',
                    role: 'customer'
                });
            });
    });
});

// Get user profile
app.get('/api/auth/profile/:id', (req, res) => {
    const userId = req.params.id;

    db.get('SELECT user_id, full_name, email, phone, role FROM users WHERE user_id = ?', [userId], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.json({
            success: true,
            user: {
                id: user.user_id,
                full_name: user.full_name,
                email: user.email,
                phone: user.phone,
                role: user.role
            }
        });
    });
});

// Update user profile
app.put('/api/auth/profile/:id', (req, res) => {
    const userId = req.params.id;
    const { full_name, email, phone } = req.body;

    if (!full_name || !email) {
        return res.status(400).json({
            success: false,
            message: 'Full name and email are required'
        });
    }

    // Check if email is taken by another user
    db.get('SELECT user_id FROM users WHERE email = ? AND user_id != ?', [email, userId], (err, existingUser) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (existingUser) {
            return res.status(400).json({
                success: false,
                message: 'Email is already taken by another user'
            });
        }

        // Update user profile
        db.run(
            'UPDATE users SET full_name = ?, email = ?, phone = ? WHERE user_id = ?',
            [full_name, email, phone, userId],
            function(err) {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error updating profile'
                    });
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        message: 'User not found'
                    });
                }

                res.json({
                    success: true,
                    message: 'Profile updated successfully'
                });
            }
        );
    });
});

// Change password
app.put('/api/auth/change-password/:id', (req, res) => {
    const userId = req.params.id;
    const { current_password, new_password } = req.body;

    if (!current_password || !new_password) {
        return res.status(400).json({
            success: false,
            message: 'Current password and new password are required'
        });
    }

    // Get user's current password
    db.get('SELECT password FROM users WHERE user_id = ?', [userId], (err, user) => {
        if (err) {
            return res.status(500).json({
                success: false,
                message: 'Database error'
            });
        }

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check current password (simple comparison since we're not using hashing in this implementation)
        if (user.password !== current_password) {
            return res.status(401).json({
                success: false,
                message: 'Current password is incorrect'
            });
        }

        // Update password
        db.run(
            'UPDATE users SET password = ? WHERE user_id = ?',
            [new_password, userId],
            function(err) {
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Error updating password'
                    });
                }

                res.json({
                    success: true,
                    message: 'Password changed successfully'
                });
            }
        );
    });
});

// ==================== PRODUCTS ROUTES ====================

// Get all products
app.get('/api/products', (req, res) => {
    db.all('SELECT * FROM products WHERE available = 1', [], (err, products) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(products);
    });
});

// Get products by category
app.get('/api/products/category/:category', (req, res) => {
    const { category } = req.params;
    db.all('SELECT * FROM products WHERE category = ? AND available = 1', [category], (err, products) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        res.json(products);
    });
});

// Get product categories
app.get('/api/products/categories', (req, res) => {
    db.all('SELECT DISTINCT category FROM products WHERE available = 1', [], (err, categories) => {
        if (err) {
            return res.status(500).json({ error: 'Database error' });
        }
        const categoryList = ['All', ...categories.map(c => c.category)];
        res.json(categoryList);
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Coffee Shop API server running on port ${PORT}`);
});
